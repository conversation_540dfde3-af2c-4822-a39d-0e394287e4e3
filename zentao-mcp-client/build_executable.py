#!/usr/bin/env python3
"""
PyInstaller打包脚本 - 创建独立可执行文件
"""

import os
import sys
import subprocess
import shutil
from pathlib import Path


def check_pyinstaller():
    """检查PyInstaller是否安装"""
    try:
        import PyInstaller
        print(f"✅ PyInstaller已安装: {PyInstaller.__version__}")
        return True
    except ImportError:
        print("❌ PyInstaller未安装，正在尝试安装...")
        try:
            subprocess.check_call([sys.executable, "-m", "pip", "install", "pyinstaller"])
            print("✅ PyInstaller安装成功!")
            import PyInstaller
            print(f"✅ PyInstaller已安装: {PyInstaller.__version__}")
            return True
        except subprocess.CalledProcessError as e:
            print(f"❌ PyInstaller安装失败: {e}")
            return False
        return False


def create_spec_file():
    """创建PyInstaller spec文件"""
    spec_content = '''# -*- mode: python ; coding: utf-8 -*-

block_cipher = None

a = Analysis(
    ['zentao-mcp/cli.py'],
    pathex=['zentao-mcp'],
    binaries=[],
    datas=[('zentao-mcp', 'zentao_mcp')],
    hiddenimports=[
        'zentao_mcp.cli',
        'zentao_mcp.config',
        'zentao_mcp.proxy',
        'zentao_mcp.logging_config',
        'zentao_mcp.log_viewer',
        'fastmcp',
        'httpx',
        'click',
        'pydantic',
        'starlette.requests',
        'starlette.responses',
        'configparser',
        'pathlib',
        'asyncio',
    ],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    collect_all=['fastmcp'],
    excludes=[
        'tkinter',
        'matplotlib',
        'numpy',
        'pandas',
        'scipy',
        'PIL',
        'PyQt5',
        'PyQt6',
        'PySide2',
        'PySide6',
    ],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='zentao-mcp',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=True,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
)
'''

    with open('zentao-mcp-client.spec', 'w', encoding='utf-8') as f:
        f.write(spec_content)

    print("✅ 创建spec文件: zentao-mcp-client.spec")


def build_executable():
    """构建可执行文件"""
    print("🏗️  开始构建可执行文件...")
    
    try:
        # 运行PyInstaller
        cmd = [
            sys.executable, '-m', 'PyInstaller',
            '--clean',
            '--noconfirm',
            'zentao-mcp-client.spec'
        ]
        
        result = subprocess.run(cmd, check=True, capture_output=True, text=True)
        print("✅ 构建成功!")
        
        # 检查输出文件
        dist_dir = Path('dist')
        if dist_dir.exists():
            exe_files = list(dist_dir.glob('zentao-mcp-client*'))
            if exe_files:
                exe_file = exe_files[0]
                size_mb = exe_file.stat().st_size / (1024 * 1024)
                print(f"📦 可执行文件: {exe_file}")
                print(f"📊 文件大小: {size_mb:.1f} MB")
                return True
        
        print("❌ 未找到生成的可执行文件")
        return False
        
    except subprocess.CalledProcessError as e:
        print(f"❌ 构建失败: {e}")
        print(f"错误输出: {e.stderr}")
        return False


def create_build_script():
    """创建构建脚本"""
    
    # Windows批处理脚本
    bat_content = '''@echo off
echo Building Zentao MCP Client executable...
python build_executable.py
if %ERRORLEVEL% EQU 0 (
    echo Build completed successfully!
    echo Executable location: dist/zentao-mcp-client.exe
) else (
    echo Build failed!
)
pause
'''
    
    with open('build.bat', 'w', encoding='utf-8') as f:
        f.write(bat_content)
    
    # Unix shell脚本
    sh_content = '''#!/bin/bash
echo "Building Zentao MCP Client executable..."
python3 build_executable.py
if [ $? -eq 0 ]; then
    echo "Build completed successfully!"
    echo "Executable location: dist/zentao-mcp-client"
else
    echo "Build failed!"
fi
'''
    
    with open('build.sh', 'w', encoding='utf-8') as f:
        f.write(sh_content)
    
    # 设置执行权限
    try:
        os.chmod('build.sh', 0o755)
    except:
        pass
    
    print("✅ 创建构建脚本: build.bat, build.sh")


def clean_build_files():
    """清理构建文件"""
    print("🧹 清理构建文件...")
    
    dirs_to_remove = ['build', '__pycache__']
    files_to_remove = ['zentao-mcp-client.spec']
    
    for dir_name in dirs_to_remove:
        if os.path.exists(dir_name):
            shutil.rmtree(dir_name)
            print(f"删除目录: {dir_name}")
    
    for file_name in files_to_remove:
        if os.path.exists(file_name):
            os.remove(file_name)
            print(f"删除文件: {file_name}")


def main():
    """主函数"""
    print("🚀 Zentao MCP Client 打包工具")
    print("=" * 50)
    
    # 检查当前目录
    if not os.path.exists('zentao-mcp'):
        print("❌ 请在项目根目录运行此脚本")
        return 1
    
    # 检查PyInstaller
    if not check_pyinstaller():
        return 1
    
    try:
        # 创建spec文件
        create_spec_file()
        
        # 构建可执行文件
        success = build_executable()
        
        if success:
            print("\n🎉 打包完成!")
            print("\n📋 生成的文件:")
            print("- dist/zentao-mcp (可执行文件)")
            print("\n🚀 使用方法:")
            print("1. 复制可执行文件到目标机器")
            print("2. 运行: ./zentao-mcp configure")
            print("3. 启动: ./zentao-mcp start")
            
            # 创建构建脚本
            create_build_script()
            
            return 0
        else:
            print("❌ 打包失败")
            return 1
            
    except KeyboardInterrupt:
        print("\n⚠️  用户中断")
        return 1
    except Exception as e:
        print(f"❌ 打包过程中出错: {e}")
        return 1
    finally:
        # 清理临时文件
        clean_build_files()


if __name__ == "__main__":
    sys.exit(main())
