#!/usr/bin/env python3
"""
Zentao MCP Client 交互式部署脚本
提供用户友好的部署菜单和统一的部署体验
"""

import os
import sys
import subprocess
import json
from pathlib import Path
from typing import Dict, List, Tuple, Optional
from dataclasses import dataclass
from enum import Enum

class InstallMethod(Enum):
    SOURCE = "source"
    PIP = "pip" 
    EXECUTABLE = "executable"

class RuntimeEnv(Enum):
    PC = "pc"
    CONTAINER = "container"

class ExecutionMode(Enum):
    STDIO = "stdio"
    HTTP = "http"
    SSE = "sse"

class Environment(Enum):
    DEV = "dev"
    TEST = "test"
    PROD = "prod"

@dataclass
class DeploymentConfig:
    """部署配置"""
    environment: Environment
    install_method: InstallMethod
    runtime_env: RuntimeEnv
    execution_mode: ExecutionMode
    host: str = "localhost"
    port: int = 8080
    force_rebuild: bool = False
    verbose: bool = False

class Colors:
    """终端颜色"""
    RED = '\033[0;31m'
    GREEN = '\033[0;32m'
    YELLOW = '\033[1;33m'
    BLUE = '\033[0;34m'
    PURPLE = '\033[0;35m'
    CYAN = '\033[0;36m'
    WHITE = '\033[1;37m'
    NC = '\033[0m'  # No Color

class Logger:
    """日志输出"""
    
    @staticmethod
    def info(msg: str):
        print(f"{Colors.BLUE}[INFO]{Colors.NC} {msg}")
    
    @staticmethod
    def success(msg: str):
        print(f"{Colors.GREEN}[SUCCESS]{Colors.NC} {msg}")
    
    @staticmethod
    def warning(msg: str):
        print(f"{Colors.YELLOW}[WARNING]{Colors.NC} {msg}")
    
    @staticmethod
    def error(msg: str):
        print(f"{Colors.RED}[ERROR]{Colors.NC} {msg}")
    
    @staticmethod
    def debug(msg: str, verbose: bool = False):
        if verbose:
            print(f"{Colors.PURPLE}[DEBUG]{Colors.NC} {msg}")

class InteractiveDeployer:
    """交互式部署器"""
    
    def __init__(self):
        self.script_dir = Path(__file__).parent
        self.project_root = self.script_dir.parent
        self.config: Optional[DeploymentConfig] = None
        
    def show_banner(self):
        """显示欢迎横幅"""
        print(f"\n{Colors.CYAN}{'='*60}{Colors.NC}")
        print(f"{Colors.WHITE}    Zentao MCP Client 交互式部署脚本{Colors.NC}")
        print(f"{Colors.CYAN}{'='*60}{Colors.NC}\n")
    
    def show_main_menu(self) -> int:
        """显示主菜单并获取用户选择"""
        print(f"{Colors.WHITE}请选择部署方式：{Colors.NC}\n")
        
        print(f"{Colors.GREEN}1. [默认] 开发环境{Colors.NC}")
        print(f"   描述: 一键部署用于本地开发的标准环境")
        print(f"   配置: 源码运行 + PC环境 + STDIO模式\n")
        
        print(f"{Colors.BLUE}2. 测试环境{Colors.NC}")
        print(f"   描述: 部署到准生产环境进行测试")
        print(f"   配置: 可执行文件 + 容器环境 + HTTP模式\n")
        
        print(f"{Colors.PURPLE}3. 生产环境{Colors.NC}")
        print(f"   描述: 部署到正式生产环境")
        print(f"   配置: 可执行文件 + 容器环境 + HTTP模式\n")
        
        print(f"{Colors.YELLOW}4. 自定义部署{Colors.NC}")
        print(f"   描述: 通过交互式问答来自定义所有部署参数\n")
        
        while True:
            try:
                choice = input(f"{Colors.CYAN}请输入选择 [1-4] (默认: 1): {Colors.NC}").strip()
                if not choice:
                    return 1
                choice_num = int(choice)
                if 1 <= choice_num <= 4:
                    return choice_num
                else:
                    Logger.error("请输入 1-4 之间的数字")
            except ValueError:
                Logger.error("请输入有效的数字")
    
    def get_predefined_config(self, choice: int) -> DeploymentConfig:
        """获取预定义配置"""
        configs = {
            1: DeploymentConfig(
                environment=Environment.DEV,
                install_method=InstallMethod.SOURCE,
                runtime_env=RuntimeEnv.PC,
                execution_mode=ExecutionMode.STDIO
            ),
            2: DeploymentConfig(
                environment=Environment.TEST,
                install_method=InstallMethod.EXECUTABLE,
                runtime_env=RuntimeEnv.CONTAINER,
                execution_mode=ExecutionMode.HTTP,
                host="0.0.0.0",
                port=8080
            ),
            3: DeploymentConfig(
                environment=Environment.PROD,
                install_method=InstallMethod.EXECUTABLE,
                runtime_env=RuntimeEnv.CONTAINER,
                execution_mode=ExecutionMode.HTTP,
                host="0.0.0.0",
                port=8080
            )
        }
        return configs[choice]
    
    def get_custom_config(self) -> DeploymentConfig:
        """获取自定义配置"""
        print(f"\n{Colors.WHITE}=== 自定义部署配置 ==={Colors.NC}\n")
        
        # 选择环境
        print(f"{Colors.CYAN}步骤 1/4: 选择环境{Colors.NC}")
        env_choice = self._get_choice(
            "请选择环境:",
            ["dev (开发环境)", "test (测试环境)", "prod (生产环境)"],
            ["dev", "test", "prod"]
        )
        environment = Environment(env_choice)
        
        print(f"✓ 已选择环境: {Colors.GREEN}{env_choice}{Colors.NC}\n")
        
        # 选择安装方式
        print(f"{Colors.CYAN}步骤 2/4: 选择安装方式{Colors.NC}")
        install_choice = self._get_choice(
            "请选择安装方式:",
            ["source (源码运行)", "pip (pip安装)", "executable (打包文件安装)"],
            ["source", "pip", "executable"]
        )
        install_method = InstallMethod(install_choice)
        
        print(f"✓ 已选择安装方式: {Colors.GREEN}{install_choice}{Colors.NC}\n")
        
        # 选择运行环境
        print(f"{Colors.CYAN}步骤 3/4: 选择运行环境{Colors.NC}")
        runtime_choice = self._get_choice(
            "请选择运行环境:",
            ["pc (PC环境)", "container (容器环境)"],
            ["pc", "container"]
        )
        runtime_env = RuntimeEnv(runtime_choice)
        
        print(f"✓ 已选择运行环境: {Colors.GREEN}{runtime_choice}{Colors.NC}\n")
        
        # 选择运行方式
        print(f"{Colors.CYAN}步骤 4/4: 选择运行方式{Colors.NC}")
        exec_choice = self._get_choice(
            "请选择运行方式:",
            ["stdio (标准输入输出)", "http (HTTP服务)", "sse (服务器发送事件)"],
            ["stdio", "http", "sse"]
        )
        execution_mode = ExecutionMode(exec_choice)
        
        print(f"✓ 已选择运行方式: {Colors.GREEN}{exec_choice}{Colors.NC}\n")
        
        # 配置网络参数
        host = "localhost"
        port = 8080
        if execution_mode in [ExecutionMode.HTTP, ExecutionMode.SSE]:
            print(f"{Colors.CYAN}网络配置:{Colors.NC}")
            host_input = input(f"监听地址 (默认: localhost): ").strip()
            if host_input:
                host = host_input
            
            port_input = input(f"端口号 (默认: 8080): ").strip()
            if port_input:
                try:
                    port = int(port_input)
                except ValueError:
                    Logger.warning("端口号无效，使用默认值 8080")
                    port = 8080
        
        return DeploymentConfig(
            environment=environment,
            install_method=install_method,
            runtime_env=runtime_env,
            execution_mode=execution_mode,
            host=host,
            port=port
        )
    
    def _get_choice(self, prompt: str, options: List[str], values: List[str]) -> str:
        """获取用户选择"""
        print(f"{prompt}")
        for i, option in enumerate(options, 1):
            print(f"  {i}. {option}")
        
        while True:
            try:
                choice = input(f"请选择 [1-{len(options)}]: ").strip()
                choice_num = int(choice)
                if 1 <= choice_num <= len(options):
                    return values[choice_num - 1]
                else:
                    Logger.error(f"请输入 1-{len(options)} 之间的数字")
            except ValueError:
                Logger.error("请输入有效的数字")
    
    def show_config_summary(self, config: DeploymentConfig):
        """显示配置摘要"""
        print(f"\n{Colors.WHITE}=== 部署配置摘要 ==={Colors.NC}")
        print(f"环境: {Colors.GREEN}{config.environment.value}{Colors.NC}")
        print(f"安装方式: {Colors.GREEN}{config.install_method.value}{Colors.NC}")
        print(f"运行环境: {Colors.GREEN}{config.runtime_env.value}{Colors.NC}")
        print(f"运行方式: {Colors.GREEN}{config.execution_mode.value}{Colors.NC}")
        
        if config.execution_mode != ExecutionMode.STDIO:
            print(f"监听地址: {Colors.GREEN}{config.host}:{config.port}{Colors.NC}")
        
        print()
    
    def confirm_deployment(self) -> bool:
        """确认部署"""
        while True:
            confirm = input(f"{Colors.YELLOW}确认开始部署？ [y/N]: {Colors.NC}").strip().lower()
            if confirm in ['y', 'yes']:
                return True
            elif confirm in ['n', 'no', '']:
                return False
            else:
                Logger.error("请输入 y 或 n")

    def execute_deployment(self):
        """执行部署"""
        if not self.config:
            raise ValueError("部署配置未设置")

        Logger.info("执行部署步骤: BUILD → START")

        # 步骤1: BUILD (构建+安装)
        self._execute_build()

        # 步骤2: START (启动服务)
        self._execute_start()

    def _execute_build(self):
        """执行构建步骤"""
        Logger.info(f"[BUILD] 构建 {self.config.install_method.value} 制品...")

        if self.config.install_method == InstallMethod.SOURCE:
            self._build_source()
        elif self.config.install_method == InstallMethod.PIP:
            self._build_pip()
        elif self.config.install_method == InstallMethod.EXECUTABLE:
            self._build_executable()

        Logger.success("[BUILD] 构建完成")

    def _execute_start(self):
        """执行启动步骤"""
        Logger.info(f"[START] 启动 {self.config.execution_mode.value} 服务...")

        if self.config.runtime_env == RuntimeEnv.CONTAINER:
            self._start_container()
        else:
            self._start_process()

        Logger.success("[START] 服务启动完成")

    def _build_source(self):
        """构建源码制品"""
        Logger.info("安装Python依赖...")
        self._run_command(["uv", "sync"], cwd=self.script_dir)

    def _build_pip(self):
        """构建pip制品"""
        Logger.info("安装Python包到系统...")
        self._run_command(["pip", "install", "-e", "."], cwd=self.script_dir)

    def _build_executable(self):
        """构建可执行文件制品"""
        Logger.info("构建二进制文件...")

        # 检查PyInstaller
        try:
            subprocess.run(["python3", "-c", "import PyInstaller"],
                         check=True, capture_output=True)
        except subprocess.CalledProcessError:
            Logger.info("安装PyInstaller...")
            self._run_command(["pip", "install", "pyinstaller"])

        # 构建可执行文件
        self._run_command(["python3", "build_executable.py"], cwd=self.script_dir)

        # 安装到系统路径
        exe_path = self.script_dir / "dist" / "zentao-mcp-client"
        if exe_path.exists():
            install_dir = Path("/usr/local/bin")
            target_path = install_dir / "zentao-mcp-client"

            try:
                # 尝试复制到系统目录
                import shutil
                shutil.copy2(exe_path, target_path)
                target_path.chmod(0o755)
                Logger.success(f"可执行文件已安装到: {target_path}")
            except PermissionError:
                Logger.warning("无权限安装到系统目录，请手动执行:")
                Logger.warning(f"sudo cp {exe_path} /usr/local/bin/")
                Logger.warning("sudo chmod +x /usr/local/bin/zentao-mcp-client")
        else:
            raise RuntimeError("可执行文件构建失败")

    def _start_container(self):
        """启动容器服务"""
        Logger.info("启动容器服务...")

        # 检查容器引擎
        container_engine = self._detect_container_engine()
        compose_cmd = self._get_compose_command(container_engine)

        # 设置环境变量
        env = os.environ.copy()
        env["ENVIRONMENT"] = self.config.environment.value

        # 构建和启动容器
        compose_file = self.script_dir / "config" / "docker-compose.yml"
        env_file = self.script_dir / "config" / "environments" / f"{self.config.environment.value}.env"

        if not compose_file.exists():
            raise FileNotFoundError(f"Compose配置文件不存在: {compose_file}")
        if not env_file.exists():
            raise FileNotFoundError(f"环境配置文件不存在: {env_file}")

        # 构建镜像
        build_cmd = compose_cmd + ["-f", str(compose_file), "--env-file", str(env_file), "build"]
        self._run_command(build_cmd, env=env, cwd=self.script_dir)

        # 启动服务
        up_cmd = compose_cmd + ["-f", str(compose_file), "--env-file", str(env_file), "up", "-d"]
        self._run_command(up_cmd, env=env, cwd=self.script_dir)

    def _start_process(self):
        """启动进程服务"""
        Logger.info("启动进程服务...")

        # 构建启动命令
        start_args = ["start", "--mode", self.config.execution_mode.value]

        if self.config.execution_mode == ExecutionMode.HTTP:
            start_args.extend(["--host", self.config.host, "--port", str(self.config.port)])
        elif self.config.execution_mode == ExecutionMode.SSE:
            start_args.extend(["--port", str(self.config.port)])

        # 根据安装方式选择启动命令
        if self.config.install_method == InstallMethod.SOURCE:
            cmd = ["uv", "run", "python", "-m", "zentao_mcp_client"] + start_args
            self._run_command(cmd, cwd=self.script_dir)
        elif self.config.install_method == InstallMethod.PIP:
            cmd = ["zentao-mcp-client"] + start_args
            self._run_command(cmd)
        elif self.config.install_method == InstallMethod.EXECUTABLE:
            exe_path = Path("/usr/local/bin/zentao-mcp-client")
            if exe_path.exists():
                cmd = [str(exe_path)] + start_args
            else:
                # 回退到本地可执行文件
                local_exe = self.script_dir / "dist" / "zentao-mcp-client"
                if local_exe.exists():
                    cmd = [str(local_exe)] + start_args
                else:
                    raise RuntimeError("找不到zentao-mcp-client可执行文件")

            self._run_command(cmd)

    def _detect_container_engine(self) -> str:
        """检测容器引擎"""
        try:
            subprocess.run(["podman", "--version"], check=True, capture_output=True)
            Logger.info("检测到 Podman 容器引擎")
            return "podman"
        except (subprocess.CalledProcessError, FileNotFoundError):
            pass

        try:
            subprocess.run(["docker", "--version"], check=True, capture_output=True)
            Logger.info("检测到 Docker 容器引擎")
            return "docker"
        except (subprocess.CalledProcessError, FileNotFoundError):
            raise RuntimeError("未找到可用的容器引擎 (Docker 或 Podman)")

    def _get_compose_command(self, container_engine: str) -> List[str]:
        """获取compose命令"""
        if container_engine == "podman":
            # 优先使用podman-compose
            try:
                subprocess.run(["podman-compose", "--version"], check=True, capture_output=True)
                return ["podman-compose"]
            except (subprocess.CalledProcessError, FileNotFoundError):
                pass

            # 回退到docker-compose
            try:
                subprocess.run(["docker-compose", "--version"], check=True, capture_output=True)
                Logger.warning("使用 docker-compose 与 Podman（可能存在兼容性问题）")
                return ["docker-compose"]
            except (subprocess.CalledProcessError, FileNotFoundError):
                raise RuntimeError("Podman环境需要安装 podman-compose 或 docker-compose")
        else:
            # Docker环境：优先使用 docker compose (V2)
            try:
                subprocess.run(["docker", "compose", "version"], check=True, capture_output=True)
                return ["docker", "compose"]
            except subprocess.CalledProcessError:
                pass

            # 回退到docker-compose (V1)
            try:
                subprocess.run(["docker-compose", "--version"], check=True, capture_output=True)
                return ["docker-compose"]
            except (subprocess.CalledProcessError, FileNotFoundError):
                raise RuntimeError("Docker环境需要安装 Docker Compose")

    def _run_command(self, cmd: List[str], cwd: Optional[Path] = None, env: Optional[Dict] = None):
        """运行命令"""
        cmd_str = " ".join(cmd)
        Logger.debug(f"执行命令: {cmd_str}", self.config.verbose if self.config else False)

        try:
            result = subprocess.run(
                cmd,
                cwd=cwd,
                env=env,
                check=True,
                capture_output=not (self.config and self.config.verbose),
                text=True
            )

            if self.config and self.config.verbose and result.stdout:
                print(result.stdout)

        except subprocess.CalledProcessError as e:
            error_msg = f"命令执行失败: {cmd_str}"
            if e.stderr:
                error_msg += f"\n错误信息: {e.stderr}"
            raise RuntimeError(error_msg)

if __name__ == "__main__":
    deployer = InteractiveDeployer()
    deployer.show_banner()
    
    # 显示主菜单并获取选择
    choice = deployer.show_main_menu()
    
    # 获取配置
    if choice == 4:
        config = deployer.get_custom_config()
    else:
        config = deployer.get_predefined_config(choice)
    
    # 显示配置摘要
    deployer.show_config_summary(config)
    
    # 确认部署
    if not deployer.confirm_deployment():
        Logger.info("部署已取消")
        sys.exit(0)
    
    Logger.info("开始部署...")

    # 设置配置并执行部署
    deployer.config = config
    try:
        deployer.execute_deployment()
        Logger.success("部署完成！")
    except Exception as e:
        Logger.error(f"部署失败: {e}")
        sys.exit(1)
