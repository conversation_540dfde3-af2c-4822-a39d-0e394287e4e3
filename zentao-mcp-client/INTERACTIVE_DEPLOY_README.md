# Zentao MCP Client 交互式部署脚本

## 概述

`interactive_deploy.py` 是一个用户友好的交互式部署脚本，提供统一的部署体验和清晰的配置选项。

## 核心特性

### 统一的部署语义
- **BUILD**: 构建制品 + 安装到目标位置
- **START**: 启动服务
- **DEPLOY**: BUILD + START (一键部署)

### 四种部署选项

#### 1. [默认] 开发环境
- **配置**: 源码运行 + PC环境 + STDIO模式
- **适用**: 本地开发和调试
- **特点**: 快速启动，实时代码修改

#### 2. 测试环境
- **配置**: 可执行文件 + 容器环境 + HTTP模式
- **适用**: 准生产环境测试
- **特点**: 接近生产环境的部署方式

#### 3. 生产环境
- **配置**: 可执行文件 + 容器环境 + HTTP模式
- **适用**: 正式生产环境
- **特点**: 高性能，容器化部署

#### 4. 自定义部署
- **配置**: 交互式选择所有参数
- **适用**: 特殊需求或测试不同配置
- **特点**: 完全可定制

## 使用方法

### 推荐使用方式 (Shell脚本入口)
```bash
cd zentao-mcp-client
./interactive_deploy.sh
```

### 直接使用Python脚本
```bash
cd zentao-mcp-client
python3 interactive_deploy.py
```

### Shell脚本选项
```bash
./interactive_deploy.sh --help          # 显示帮助信息
./interactive_deploy.sh --version       # 显示版本信息
./interactive_deploy.sh --check-deps    # 检查依赖环境
./interactive_deploy.sh --python-path python3.11  # 指定Python版本
```

### 交互流程
1. 选择部署方式 (1-4)
2. 如果选择自定义，逐步配置参数
3. 查看配置摘要
4. 确认开始部署
5. 自动执行 BUILD → START 流程

## 配置参数说明

### 环境 (Environment)
- **dev**: 开发环境，使用开发配置
- **test**: 测试环境，使用测试配置
- **prod**: 生产环境，使用生产配置

### 安装方式 (Install Method)
- **source**: 源码运行，使用 `uv sync` 安装依赖
- **pip**: pip安装，使用 `pip install -e .` 安装包
- **executable**: 可执行文件，使用 PyInstaller 构建二进制文件

### 运行环境 (Runtime Environment)
- **pc**: PC环境，直接在主机上运行进程
- **container**: 容器环境，使用 Docker/Podman 运行

### 运行方式 (Execution Mode)
- **stdio**: 标准输入输出模式，适合开发调试
- **http**: HTTP服务模式，提供REST API接口
- **sse**: 服务器发送事件模式，支持实时推送

## 部署逻辑详解

### BUILD 阶段
根据不同的安装方式执行相应的构建和安装操作：

**source制品**:
```bash
cd project_dir && uv sync
```

**pip制品**:
```bash
cd project_dir && pip install -e .
```

**executable制品**:
```bash
# 构建二进制文件
python3 build_executable.py
# 安装到系统路径
cp dist/zentao-mcp-client /usr/local/bin/
chmod +x /usr/local/bin/zentao-mcp-client
```

### START 阶段
根据运行环境和方式启动服务：

**PC环境**:
- source: `uv run python -m zentao_mcp_client start --mode {mode}`
- pip: `zentao-mcp-client start --mode {mode}`
- executable: `/usr/local/bin/zentao-mcp-client start --mode {mode}`

**容器环境**:
```bash
docker-compose -f config/docker-compose.yml --env-file config/environments/{env}.env build
docker-compose -f config/docker-compose.yml --env-file config/environments/{env}.env up -d
```

## 依赖要求

### 基础依赖
- Python 3.10+
- uv (Python包管理器)

### 可选依赖
- PyInstaller (构建可执行文件时需要)
- Docker 或 Podman (容器部署时需要)
- docker-compose 或 podman-compose (容器部署时需要)

## 错误处理

脚本包含完善的错误处理机制：
- 自动检测缺失的依赖
- 提供清晰的错误信息
- 支持权限不足时的提示
- 容器引擎自动检测和回退

## 脚本文件说明

### 文件结构
- `interactive_deploy.sh`: Shell脚本入口，提供统一的命令行接口
- `interactive_deploy.py`: Python核心脚本，实现交互式部署逻辑
- `INTERACTIVE_DEPLOY_README.md`: 详细使用说明文档

### 与原有脚本的关系
- **保持兼容**: 原有的 `deploy.sh` 脚本继续可用
- **功能增强**: 提供更友好的交互界面
- **统一语义**: 所有制品使用相同的部署概念
- **向后兼容**: 复用现有的配置文件和Docker配置
- **统一入口**: Shell脚本入口与其他项目保持一致

## 故障排除

### 常见问题

1. **权限不足**
   ```
   sudo cp dist/zentao-mcp-client /usr/local/bin/
   sudo chmod +x /usr/local/bin/zentao-mcp-client
   ```

2. **容器引擎未找到**
   ```bash
   # 安装Docker
   curl -fsSL https://get.docker.com -o get-docker.sh
   sh get-docker.sh
   
   # 或安装Podman
   brew install podman  # macOS
   ```

3. **PyInstaller缺失**
   ```bash
   pip install pyinstaller
   ```

## 开发说明

脚本采用面向对象设计，主要类：
- `InteractiveDeployer`: 主要的部署器类
- `DeploymentConfig`: 部署配置数据类
- `Logger`: 日志输出工具类

扩展新功能时，可以：
1. 添加新的枚举类型
2. 扩展 `DeploymentConfig` 数据类
3. 在 `InteractiveDeployer` 中添加新的方法
