#!/bin/bash
# ============================================================================
# Zentao MCP Client 统一部署脚本 v2.0
# 支持快速部署、交互式配置和传统命令行三种模式
# ============================================================================

# 脚本配置
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"
SERVICE_DIR="$SCRIPT_DIR"

# 默认参数
ENVIRONMENT="dev"
ACTION="deploy"
DEPLOYMENT_TYPE=""
SERVICE_MODE="stdio"
HOST="localhost"
PORT=8080
FORCE_REBUILD=false
VERBOSE=false
CONTAINER_ENGINE=""
COMPOSE_CMD=""

# 加载公共函数库
if [[ -f "$PROJECT_ROOT/scripts/common.sh" ]]; then
    source "$PROJECT_ROOT/scripts/common.sh"
else
    echo "错误: 找不到公共函数库 $PROJECT_ROOT/scripts/common.sh"
    exit 1
fi

# 显示横幅
show_banner() {
    echo -e "\n${BLUE}============================================================${NC}"
    echo -e "${WHITE}    Zentao MCP Client 部署脚本 v2.0${NC}"
    echo -e "${BLUE}============================================================${NC}\n"
}

# 显示主菜单
show_main_menu() {
    echo -e "${WHITE}选择操作模式:${NC}\n"

    echo -e "${GREEN}1. 快速部署 (使用预设配置)${NC}"
    echo -e "   描述: 一键部署常用环境配置\n"

    echo -e "${BLUE}2. 交互式配置 (自定义所有参数)${NC}"
    echo -e "   描述: 逐步选择所有部署参数，带配置验证\n"

    echo -e "${YELLOW}3. 传统命令行模式 (显示帮助信息)${NC}"
    echo -e "   描述: 兼容原有命令行用法\n"

    while true; do
        read -p "请选择 [1-3] (默认: 1): " choice
        case ${choice:-1} in
            1) return 1 ;;
            2) return 2 ;;
            3) return 3 ;;
            *) log_error "请输入 1-3 之间的数字" ;;
        esac
    done
}

# 显示快速部署菜单
show_quick_deploy_menu() {
    echo -e "\n${WHITE}=== 快速部署选项 ===${NC}\n"

    echo -e "${GREEN}1. [推荐] 开发环境${NC}"
    echo -e "   配置: dev + source + pc + stdio\n"

    echo -e "${BLUE}2. 测试环境${NC}"
    echo -e "   配置: test + executable + container + http\n"

    echo -e "${PURPLE}3. 生产环境${NC}"
    echo -e "   配置: prod + executable + container + http\n"

    while true; do
        read -p "请选择 [1-3] (默认: 1): " choice
        case ${choice:-1} in
            1)
                ENVIRONMENT="dev"
                DEPLOYMENT_TYPE="source"
                RUNTIME_ENV="pc"
                SERVICE_MODE="stdio"
                return 0
                ;;
            2)
                ENVIRONMENT="test"
                DEPLOYMENT_TYPE="executable"
                RUNTIME_ENV="container"
                SERVICE_MODE="http"
                HOST="0.0.0.0"
                return 0
                ;;
            3)
                ENVIRONMENT="prod"
                DEPLOYMENT_TYPE="executable"
                RUNTIME_ENV="container"
                SERVICE_MODE="http"
                HOST="0.0.0.0"
                return 0
                ;;
            *) log_error "请输入 1-3 之间的数字" ;;
        esac
    done
}

# 交互式配置
interactive_config() {
    echo -e "\n${WHITE}=== 交互式配置向导 ===${NC}\n"

    # 步骤1: 选择环境
    echo -e "${CYAN}步骤 1/4: 选择环境${NC}"
    echo "  1. dev (开发环境)"
    echo "  2. test (测试环境)"
    echo "  3. prod (生产环境)"

    while true; do
        read -p "请选择 [1-3]: " choice
        case $choice in
            1) ENVIRONMENT="dev"; break ;;
            2) ENVIRONMENT="test"; break ;;
            3) ENVIRONMENT="prod"; break ;;
            *) log_error "请输入 1-3 之间的数字" ;;
        esac
    done
    echo -e "✓ 已选择环境: ${GREEN}$ENVIRONMENT${NC}\n"

    # 步骤2: 选择安装方式
    echo -e "${CYAN}步骤 2/4: 选择安装方式${NC}"
    echo "  1. source (源码运行)"
    echo "  2. pip (pip安装)"
    echo "  3. executable (可执行文件)"

    while true; do
        read -p "请选择 [1-3]: " choice
        case $choice in
            1) DEPLOYMENT_TYPE="source"; break ;;
            2) DEPLOYMENT_TYPE="pip"; break ;;
            3) DEPLOYMENT_TYPE="executable"; break ;;
            *) log_error "请输入 1-3 之间的数字" ;;
        esac
    done
    echo -e "✓ 已选择安装方式: ${GREEN}$DEPLOYMENT_TYPE${NC}\n"

    # 步骤3: 选择运行环境
    echo -e "${CYAN}步骤 3/4: 选择运行环境${NC}"
    echo "  1. pc (PC环境)"
    echo "  2. container (容器环境)"

    while true; do
        read -p "请选择 [1-2]: " choice
        case $choice in
            1) RUNTIME_ENV="pc"; break ;;
            2) RUNTIME_ENV="container"; break ;;
            *) log_error "请输入 1-2 之间的数字" ;;
        esac
    done
    echo -e "✓ 已选择运行环境: ${GREEN}$RUNTIME_ENV${NC}\n"

    # 步骤4: 选择运行方式 (带验证)
    while true; do
        echo -e "${CYAN}步骤 4/4: 选择运行方式${NC}"
        echo "  1. stdio (标准输入输出)"
        echo "  2. http (HTTP服务)"
        echo "  3. sse (服务器发送事件)"

        read -p "请选择 [1-3]: " choice
        case $choice in
            1) SERVICE_MODE="stdio" ;;
            2) SERVICE_MODE="http" ;;
            3) SERVICE_MODE="sse" ;;
            *) log_error "请输入 1-3 之间的数字"; continue ;;
        esac

        # 配置验证
        if validate_config "$ENVIRONMENT" "$DEPLOYMENT_TYPE" "$RUNTIME_ENV" "$SERVICE_MODE"; then
            echo -e "✓ 已选择运行方式: ${GREEN}$SERVICE_MODE${NC}\n"
            break
        else
            echo -e "${RED}[配置验证] ❌ 配置组合无效，请重新选择${NC}\n"
        fi
    done

    # 网络配置
    if [[ "$SERVICE_MODE" == "http" || "$SERVICE_MODE" == "sse" ]]; then
        echo -e "${CYAN}网络配置:${NC}"
        read -p "监听地址 (默认: localhost): " host_input
        HOST=${host_input:-localhost}

        read -p "端口号 (默认: 8080): " port_input
        if [[ -n "$port_input" && "$port_input" =~ ^[0-9]+$ ]]; then
            PORT=$port_input
        else
            PORT=8080
        fi
        echo
    fi
}

# 配置验证
validate_config() {
    local env=$1 type=$2 runtime=$3 mode=$4

    # 规则1: 容器环境不支持stdio模式
    if [[ "$runtime" == "container" && "$mode" == "stdio" ]]; then
        log_error "容器环境不支持stdio模式，请选择http或sse"
        return 1
    fi

    # 规则2: 容器环境只支持executable安装
    if [[ "$runtime" == "container" && "$type" != "executable" ]]; then
        log_error "容器环境只支持executable安装方式"
        return 1
    fi

    return 0
}

# 显示配置摘要
show_config_summary() {
    echo -e "${WHITE}=== 部署配置摘要 ===${NC}"
    echo -e "环境: ${GREEN}$ENVIRONMENT${NC}"
    echo -e "安装方式: ${GREEN}$DEPLOYMENT_TYPE${NC}"
    echo -e "运行环境: ${GREEN}$RUNTIME_ENV${NC}"
    echo -e "运行方式: ${GREEN}$SERVICE_MODE${NC}"

    if [[ "$SERVICE_MODE" != "stdio" ]]; then
        echo -e "监听地址: ${GREEN}$HOST:$PORT${NC}"
    fi
    echo
}

# 确认部署
confirm_deployment() {
    while true; do
        read -p "确认开始部署？ [y/N]: " confirm
        case $confirm in
            [Yy]|[Yy][Ee][Ss]) return 0 ;;
            [Nn]|[Nn][Oo]|"") return 1 ;;
            *) log_error "请输入 y 或 n" ;;
        esac
    done
}

# 显示传统帮助信息
show_traditional_help() {
    cat << 'EOF'
Zentao MCP Client 部署脚本 v2.0

用法: ./deploy.sh [选项] <环境> [动作]

环境:
  dev       开发环境 (默认) - 源码运行，项目配置
  test      测试环境 - 容器部署，环境变量配置
  prod      生产环境 - 容器部署，系统服务

动作:
  deploy    部署客户端服务 (默认)
  build     构建制品
  start     启动服务
  stop      停止服务
  restart   重启服务
  status    查看服务状态
  config    配置向导
  test      运行连接测试
  clean     清理环境

选项:
  -t, --type TYPE       部署类型 (source|pip|executable|container)
  -m, --mode MODE       服务模式 (stdio|http|sse)
  --host HOST           HTTP模式监听地址 (默认: localhost)
  --port PORT           HTTP/SSE模式端口 (默认: 8080)
  -f, --force           强制重新构建
  -v, --verbose         详细输出模式
  -h, --help            显示帮助信息

快速模式:
  quick [1-3]           快速部署 (1=dev, 2=test, 3=prod)
  -q [1-3]              快速部署简写
  interactive           交互式配置
  -i                    交互式配置简写

示例:
  ./deploy.sh                           # 启动主菜单
  ./deploy.sh quick 1                   # 快速部署开发环境
  ./deploy.sh -i                        # 交互式配置
  ./deploy.sh dev deploy                # 传统命令行模式
  ./deploy.sh test start --mode=http    # 启动HTTP服务
  ./deploy.sh prod build --force        # 强制重新构建

EOF
}

# 执行进度显示
execute_with_progress() {
    local description=$1
    local command=$2
    local show_output=${3:-"on_error"}

    log_info "$description"

    if [[ "$VERBOSE" == "true" || "$show_output" == "always" ]]; then
        eval "$command"
    else
        if eval "$command" >/dev/null 2>&1; then
            return 0
        else
            local exit_code=$?
            log_error "$description 失败"
            if [[ "$show_output" == "on_error" ]]; then
                log_error "重新执行以显示详细错误信息..."
                eval "$command"
            fi
            return $exit_code
        fi
    fi
}

# 源码部署
deploy_from_source() {
    local env=$1
    cd "$SERVICE_DIR"
    execute_with_progress "安装Python依赖 (uv sync)" "uv sync" "always"
    log_success "源码部署完成"
}

# pip安装部署
deploy_with_pip() {
    local env=$1
    cd "$SERVICE_DIR"
    execute_with_progress "安装zentao-mcp包 (pip)" "pip install -e ." "always"
    log_success "pip部署完成"
}

# 构建可执行文件
build_executable() {
    local env=$1
    cd "$SERVICE_DIR"

    # 检查PyInstaller
    if ! python3 -c "import PyInstaller" 2>/dev/null; then
        execute_with_progress "安装PyInstaller" "pip install pyinstaller" "always"
    fi

    # 运行构建脚本
    execute_with_progress "构建 $env 环境可执行文件" "python3 build_executable.py" "always"

    # 检查构建结果
    if [[ -f "dist/zentao-mcp" ]]; then
        local size_mb=$(du -m "dist/zentao-mcp" | cut -f1)
        log_success "可执行文件构建完成 (${size_mb}MB)"
    else
        log_error "可执行文件构建失败"
        return 1
    fi
}

# 可执行文件部署
deploy_executable() {
    local env=$1
    log_info "使用可执行文件部署 $env 环境..."
    cd "$SERVICE_DIR"

    # 如果可执行文件不存在，先构建
    if [[ ! -f "dist/zentao-mcp" ]] || [[ "$FORCE_REBUILD" == "true" ]]; then
        build_executable "$env"
    fi

    # 复制到系统目录
    local install_dir="/usr/local/bin"
    if [[ -w "$install_dir" ]]; then
        cp "dist/zentao-mcp" "$install_dir/"
        chmod +x "$install_dir/zentao-mcp"
        log_success "可执行文件已安装到: $install_dir/zentao-mcp"
    else
        log_warning "无权限安装到系统目录，请手动复制:"
        log_warning "sudo cp dist/zentao-mcp /usr/local/bin/"
    fi
}

# 执行部署
execute_deployment() {
    log_info "执行部署步骤: BUILD → START"

    # 确定部署类型
    if [[ "$RUNTIME_ENV" == "container" ]]; then
        DEPLOYMENT_TYPE="container"
    fi

    # BUILD阶段
    case $DEPLOYMENT_TYPE in
        source)
            deploy_from_source "$ENVIRONMENT"
            ;;
        pip)
            deploy_with_pip "$ENVIRONMENT"
            ;;
        executable)
            deploy_executable "$ENVIRONMENT"
            ;;
        container)
            deploy_with_container "$ENVIRONMENT"
            ;;
    esac

    log_success "部署完成！"
}

# 容器化部署
deploy_with_container() {
    local env=$1
    cd "$SERVICE_DIR"

    # 检查配置文件
    local compose_file="config/docker-compose.yml"
    local env_file="config/environments/${env}.env"
    if [[ ! -f "$compose_file" ]]; then
        log_error "Compose配置文件不存在: $compose_file"
        return 1
    fi
    if [[ ! -f "$env_file" ]]; then
        log_error "环境配置文件不存在: $env_file"
        return 1
    fi

    # 检测容器引擎并设置compose命令
    detect_container_engine || exit 1
    setup_compose_command || exit 1

    # 设置环境变量
    export ENVIRONMENT="$env"

    # 构建和启动服务
    local build_args=""
    if [[ "$FORCE_REBUILD" == "true" ]]; then
        build_args="$build_args --no-cache"
    fi

    local build_cmd="$COMPOSE_CMD -f '$compose_file' --env-file '$env_file' build $build_args"
    execute_with_progress "构建 $env 环境客户端容器镜像" "$build_cmd" "always"

    local deploy_cmd="$COMPOSE_CMD -f '$compose_file' --env-file '$env_file' up -d"
    execute_with_progress "启动 $env 环境客户端容器服务" "$deploy_cmd" "always"
}

# 主函数
main() {
    # 检查是否有参数
    if [[ $# -eq 0 ]]; then
        # 无参数，启动主菜单
        show_banner
        show_main_menu
        local menu_choice=$?

        case $menu_choice in
            1)
                show_quick_deploy_menu
                ;;
            2)
                interactive_config
                ;;
            3)
                show_traditional_help
                exit 0
                ;;
        esac

        # 显示配置摘要并确认
        show_config_summary
        if confirm_deployment; then
            execute_deployment
        else
            log_info "部署已取消"
            exit 0
        fi
        return
    fi

    # 检查快速模式
    case $1 in
        quick|-q)
            show_banner
            if [[ -n "$2" && "$2" =~ ^[1-3]$ ]]; then
                case $2 in
                    1) ENVIRONMENT="dev"; DEPLOYMENT_TYPE="source"; RUNTIME_ENV="pc"; SERVICE_MODE="stdio" ;;
                    2) ENVIRONMENT="test"; DEPLOYMENT_TYPE="executable"; RUNTIME_ENV="container"; SERVICE_MODE="http"; HOST="0.0.0.0" ;;
                    3) ENVIRONMENT="prod"; DEPLOYMENT_TYPE="executable"; RUNTIME_ENV="container"; SERVICE_MODE="http"; HOST="0.0.0.0" ;;
                esac
                show_config_summary
                if confirm_deployment; then
                    execute_deployment
                else
                    log_info "部署已取消"
                fi
            else
                show_quick_deploy_menu
                show_config_summary
                if confirm_deployment; then
                    execute_deployment
                else
                    log_info "部署已取消"
                fi
            fi
            return
            ;;
        interactive|-i)
            show_banner
            interactive_config
            show_config_summary
            if confirm_deployment; then
                execute_deployment
            else
                log_info "部署已取消"
            fi
            return
            ;;
    esac

    # 传统命令行模式 - 解析参数
    parse_traditional_args "$@"
}

# 解析传统命令行参数
parse_traditional_args() {
    # 这里需要实现完整的传统命令行解析逻辑
    # 由于篇幅限制，先显示帮助信息
    show_traditional_help
}

# 执行主函数
main "$@"
