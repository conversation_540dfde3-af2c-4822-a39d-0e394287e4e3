[project]
name = "zentao-mcp-client"

version = "0.1.0"
description = "Zentao MCP Client - Lightweight proxy client"
readme = "README.md"
classifiers=[
    "Development Status :: 3 - Alpha",
    "Intended Audience :: Developers",
    "License :: OSI Approved :: MIT License",
    "Operating System :: OS Independent",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.10",
    "Programming Language :: Python :: 3.11",
    "Programming Language :: Python :: 3.12",
]
authors = [
    {name = "Zentao MCP Team"}
]
dependencies = [
    "fastmcp>=0.4.0",
    "httpx>=0.25.0",
    "click>=8.1.0",
    "pydantic>=2.4.0",
    "python-dotenv>=1.0.0",
    "configparser>=6.0.0",
]
requires-python = ">=3.10"

[project.optional-dependencies]
dev = [
    "pytest>=7.4.0",
    "pytest-asyncio>=0.21.0",
    "pytest-mock>=3.11.0",
    "black>=23.0.0",
    "isort>=5.12.0",
    "mypy>=1.5.0",
    "pyinstaller>=6.0.0",
]

[project.scripts]
zentao-mcp = "zentao_mcp.cli:main"

#[project.urls]
#Homepage = ""
#Repository = ""
#Issues = ""

[build-system]
requires = ["setuptools>=61.0", "wheel"]
build-backend = "setuptools.build_meta"

[tool.setuptools.packages.find]
where = ["."]
include = ["zentao_mcp*"]

[tool.black]
line-length = 100
target-version = ['py310']

[tool.isort]
profile = "black"
line_length = 100

[tool.mypy]
python_version = "3.10"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true

[dependency-groups]
dev = [
    "pyinstaller>=6.15.0",
]
