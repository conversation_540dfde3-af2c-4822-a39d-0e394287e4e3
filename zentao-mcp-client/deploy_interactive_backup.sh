#!/bin/bash
# ============================================================================
# Zentao MCP Client 交互式部署脚本入口
# 提供统一的shell脚本入口，调用Python交互式部署脚本
# ============================================================================

# 脚本配置
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PYTHON_SCRIPT="$SCRIPT_DIR/interactive_deploy.py"

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 显示帮助信息
show_help() {
    cat << 'EOF'
Zentao MCP Client 交互式部署脚本

用法: ./interactive_deploy.sh [选项]

选项:
  -h, --help            显示帮助信息
  -v, --version         显示版本信息
  --check-deps          检查依赖环境
  --python-path PATH    指定Python解释器路径

描述:
  这是一个shell脚本入口，用于启动Python交互式部署脚本。
  提供用户友好的部署菜单和统一的部署体验。

部署选项:
  1. [默认] 开发环境 - 源码运行 + PC环境 + STDIO模式
  2. 测试环境 - 可执行文件 + 容器环境 + HTTP模式  
  3. 生产环境 - 可执行文件 + 容器环境 + HTTP模式
  4. 自定义部署 - 交互式选择所有参数

示例:
  ./interactive_deploy.sh                    # 启动交互式部署
  ./interactive_deploy.sh --check-deps       # 检查依赖环境
  ./interactive_deploy.sh --python-path python3.11  # 使用指定Python版本

更多信息请参考: INTERACTIVE_DEPLOY_README.md
EOF
}

# 显示版本信息
show_version() {
    echo "Zentao MCP Client Interactive Deploy Script v1.0.0"
    echo "Compatible with Python 3.10+"
}

# 检查Python环境
check_python() {
    local python_cmd="${PYTHON_PATH:-python3}"
    
    if ! command -v "$python_cmd" &> /dev/null; then
        log_error "Python解释器未找到: $python_cmd"
        log_error "请安装Python 3.10+或使用 --python-path 指定路径"
        return 1
    fi
    
    # 检查Python版本
    local python_version
    python_version=$($python_cmd -c "import sys; print(f'{sys.version_info.major}.{sys.version_info.minor}')")
    
    if [[ $(echo "$python_version >= 3.10" | bc -l 2>/dev/null || echo "0") -eq 0 ]]; then
        # 如果bc不可用，使用简单的版本比较
        local major minor
        IFS='.' read -r major minor <<< "$python_version"
        if [[ $major -lt 3 ]] || [[ $major -eq 3 && $minor -lt 10 ]]; then
            log_error "Python版本过低: $python_version (需要 3.10+)"
            return 1
        fi
    fi
    
    log_success "Python环境检查通过: $python_cmd ($python_version)"
    return 0
}

# 检查依赖环境
check_dependencies() {
    log_info "检查依赖环境..."
    
    # 检查Python
    if ! check_python; then
        return 1
    fi
    
    # 检查Python脚本文件
    if [[ ! -f "$PYTHON_SCRIPT" ]]; then
        log_error "Python脚本文件不存在: $PYTHON_SCRIPT"
        return 1
    fi
    
    # 检查Python脚本可执行权限
    if [[ ! -x "$PYTHON_SCRIPT" ]]; then
        log_warning "Python脚本没有执行权限，尝试添加..."
        chmod +x "$PYTHON_SCRIPT" || {
            log_error "无法添加执行权限到: $PYTHON_SCRIPT"
            return 1
        }
    fi
    
    # 检查可选依赖
    local python_cmd="${PYTHON_PATH:-python3}"
    
    # 检查uv
    if command -v uv &> /dev/null; then
        log_success "uv包管理器: 已安装"
    else
        log_warning "uv包管理器: 未安装 (源码部署时需要)"
    fi
    
    # 检查容器引擎
    if command -v docker &> /dev/null; then
        log_success "Docker: 已安装"
    elif command -v podman &> /dev/null; then
        log_success "Podman: 已安装"
    else
        log_warning "容器引擎: 未安装 (容器部署时需要Docker或Podman)"
    fi
    
    # 检查PyInstaller
    if $python_cmd -c "import PyInstaller" 2>/dev/null; then
        log_success "PyInstaller: 已安装"
    else
        log_warning "PyInstaller: 未安装 (可执行文件构建时需要)"
    fi
    
    log_success "依赖环境检查完成"
    return 0
}

# 启动Python脚本
start_python_script() {
    local python_cmd="${PYTHON_PATH:-python3}"
    
    log_info "启动交互式部署脚本..."
    
    # 切换到脚本目录
    cd "$SCRIPT_DIR" || {
        log_error "无法切换到脚本目录: $SCRIPT_DIR"
        exit 1
    }
    
    # 执行Python脚本
    exec "$python_cmd" "$PYTHON_SCRIPT" "$@"
}

# 主函数
main() {
    # 解析命令行参数
    while [[ $# -gt 0 ]]; do
        case $1 in
            -h|--help)
                show_help
                exit 0
                ;;
            -v|--version)
                show_version
                exit 0
                ;;
            --check-deps)
                check_dependencies
                exit $?
                ;;
            --python-path)
                PYTHON_PATH="$2"
                shift 2
                ;;
            --python-path=*)
                PYTHON_PATH="${1#*=}"
                shift
                ;;
            -*)
                log_error "未知选项: $1"
                log_error "使用 --help 查看帮助信息"
                exit 1
                ;;
            *)
                # 其他参数传递给Python脚本
                break
                ;;
        esac
    done
    
    # 检查基本环境
    if ! check_python; then
        exit 1
    fi
    
    # 检查Python脚本文件
    if [[ ! -f "$PYTHON_SCRIPT" ]]; then
        log_error "Python脚本文件不存在: $PYTHON_SCRIPT"
        exit 1
    fi
    
    # 启动Python脚本
    start_python_script "$@"
}

# 执行主函数
main "$@"
