"""
命令行接口测试
"""

import pytest
from unittest.mock import patch, MagicMock
from click.testing import <PERSON><PERSON><PERSON><PERSON><PERSON>

from zentao_mcp.cli import main, configure, start, info
from zentao_mcp.config import ClientConfig


class TestCLI:
    """CLI测试类"""
    
    def setup_method(self):
        """测试前设置"""
        self.runner = CliRunner()
    
    def test_main_command_help(self):
        """测试主命令帮助"""
        result = self.runner.invoke(main, ['--help'])
        assert result.exit_code == 0
        assert "Zentao MCP Client" in result.output
        assert "configure" in result.output
        assert "start" in result.output
        assert "info" in result.output
    
    @patch('zentao_mcp_client.cli.ClientConfig')
    def test_configure_command_new_config(self, mock_config_class):
        """测试配置命令 - 新配置"""
        mock_config = MagicMock(spec=ClientConfig)
        mock_config.is_configured.return_value = False
        mock_config_class.return_value = mock_config
        
        # 模拟用户输入
        user_inputs = [
            "http://localhost:8000",  # backend_url
            "test-api-key-12345"      # api_key
        ]
        
        result = self.runner.invoke(configure, input='\n'.join(user_inputs))
        
        assert result.exit_code == 0
        assert "Zentao MCP Client 配置" in result.output
        assert "配置完成" in result.output
        
        # 验证配置方法被调用
        mock_config.set_backend_url.assert_called_once_with("http://localhost:8000")
        mock_config.set_api_key.assert_called_once_with("test-api-key-12345")
    
    @patch('zentao_mcp_client.cli.ClientConfig')
    def test_configure_command_existing_config(self, mock_config_class):
        """测试配置命令 - 已有配置"""
        mock_config = MagicMock(spec=ClientConfig)
        mock_config.is_configured.return_value = True
        mock_config.get_backend_url.return_value = "http://existing.com:8000"
        mock_config.get_api_key.return_value = "existing-key"
        mock_config_class.return_value = mock_config
        
        # 用户选择不重新配置
        result = self.runner.invoke(configure, input='n\n')
        
        assert result.exit_code == 0
        assert "客户端已配置" in result.output
        assert "是否重新配置" in result.output
    
    @patch('zentao_mcp_client.cli.ClientConfig')
    def test_configure_command_reconfigure(self, mock_config_class):
        """测试配置命令 - 重新配置"""
        mock_config = MagicMock(spec=ClientConfig)
        mock_config.is_configured.return_value = True
        mock_config.get_backend_url.return_value = "http://old.com:8000"
        mock_config.get_api_key.return_value = "old-key"
        mock_config_class.return_value = mock_config
        
        # 用户选择重新配置
        user_inputs = [
            "y",                      # 确认重新配置
            "http://new.com:8000",    # 新的backend_url
            "new-api-key-12345"       # 新的api_key
        ]
        
        result = self.runner.invoke(configure, input='\n'.join(user_inputs))
        
        assert result.exit_code == 0
        assert "配置保存成功" in result.output
        
        # 验证新配置被设置
        mock_config.set_backend_url.assert_called_with("http://new.com:8000")
        mock_config.set_api_key.assert_called_with("new-api-key-12345")
    
    @patch('zentao_mcp_client.cli.ClientConfig')
    def test_info_command_configured(self, mock_config_class):
        """测试信息命令 - 已配置"""
        mock_config = MagicMock(spec=ClientConfig)
        mock_config.get_config_info.return_value = {
            "config_file": "/home/<USER>/.zentao_mcp_client/config.ini",
            "backend_url": "http://localhost:8000",
            "api_key_configured": True,
            "api_key_preview": "test-key-***45"
        }
        mock_config_class.return_value = mock_config
        
        result = self.runner.invoke(info)
        
        assert result.exit_code == 0
        assert "Zentao MCP Client 配置信息" in result.output
        assert "http://localhost:8000" in result.output
        assert "test-key-***45" in result.output
    
    @patch('zentao_mcp_client.cli.ClientConfig')
    def test_info_command_not_configured(self, mock_config_class):
        """测试信息命令 - 未配置"""
        mock_config = MagicMock(spec=ClientConfig)
        mock_config.is_configured.return_value = False  # 关键设置
        mock_config.get_config_info.return_value = {
            "config_file": "/home/<USER>/.zentao_mcp_client/config.ini",
            "backend_url": None,
            "api_key_configured": False,
            "api_key_preview": None
        }
        mock_config_class.return_value = mock_config
        
        result = self.runner.invoke(info)
        
        assert result.exit_code == 0
        assert "未配置" in result.output
    
    @patch('zentao_mcp_client.proxy.start_proxy_server')
    @patch('zentao_mcp_client.cli.ClientConfig')
    def test_start_command_configured(self, mock_config_class, mock_start_server):
        """测试启动命令 - 已配置"""
        mock_config = MagicMock(spec=ClientConfig)
        mock_config.is_configured.return_value = True
        mock_config.get_backend_url.return_value = "http://localhost:8000"
        mock_config_class.return_value = mock_config
        
        result = self.runner.invoke(start, ['--mode', 'stdio'])
        
        assert result.exit_code == 0
        assert "启动Zentao MCP客户端代理服务" in result.output
        assert "运行模式: STDIO" in result.output
        
        # 验证启动函数被调用
        mock_start_server.assert_called_once_with(
            "localhost", 8080, mock_config, "stdio"
        )
    
    @patch('zentao_mcp_client.cli.ClientConfig')
    def test_start_command_not_configured(self, mock_config_class):
        """测试启动命令 - 未配置"""
        mock_config = MagicMock(spec=ClientConfig)
        mock_config.is_configured.return_value = False
        mock_config_class.return_value = mock_config
        
        result = self.runner.invoke(start)
        
        assert result.exit_code == 0
        assert "客户端未配置" in result.output
        assert "zentao-mcp-client configure" in result.output
    
    @patch('zentao_mcp_client.proxy.start_proxy_server')
    @patch('zentao_mcp_client.cli.ClientConfig')
    def test_start_command_http_mode(self, mock_config_class, mock_start_server):
        """测试启动命令 - HTTP模式"""
        mock_config = MagicMock(spec=ClientConfig)
        mock_config.is_configured.return_value = True
        mock_config.get_backend_url.return_value = "http://localhost:8000"
        mock_config_class.return_value = mock_config
        
        result = self.runner.invoke(start, [
            '--mode', 'http',
            '--host', '0.0.0.0',
            '--port', '9090'
        ])
        
        assert result.exit_code == 0
        assert "运行模式: HTTP" in result.output
        assert "http://0.0.0.0:9090" in result.output
        
        # 验证启动函数被调用
        mock_start_server.assert_called_once_with(
            "0.0.0.0", 9090, mock_config, "http"
        )
    
    @patch('zentao_mcp_client.proxy.start_proxy_server')
    @patch('zentao_mcp_client.cli.ClientConfig')
    def test_start_command_sse_mode(self, mock_config_class, mock_start_server):
        """测试启动命令 - SSE模式"""
        mock_config = MagicMock(spec=ClientConfig)
        mock_config.is_configured.return_value = True
        mock_config.get_backend_url.return_value = "http://localhost:8000"
        mock_config_class.return_value = mock_config
        
        result = self.runner.invoke(start, ['--mode', 'sse'])
        
        assert result.exit_code == 0
        assert "运行模式: SSE" in result.output
        
        # 验证启动函数被调用
        mock_start_server.assert_called_once_with(
            "localhost", 8080, mock_config, "sse"
        )
    
    def test_start_command_invalid_mode(self):
        """测试启动命令 - 无效模式"""
        result = self.runner.invoke(start, ['--mode', 'invalid'])
        
        assert result.exit_code != 0
        assert "Invalid value for '--mode'" in result.output
    
    @patch('zentao_mcp_client.cli.ClientConfig')
    def test_configure_command_empty_input(self, mock_config_class):
        """测试配置命令 - 空输入"""
        mock_config = MagicMock(spec=ClientConfig)
        mock_config.is_configured.return_value = False
        mock_config_class.return_value = mock_config
        
        # 用户输入空值
        user_inputs = [
            "",  # 空的backend_url
            ""   # 空的api_key
        ]
        
        result = self.runner.invoke(configure, input='\n'.join(user_inputs))
        
        # 应该提示输入不能为空
        assert "不能为空" in result.output or "请输入" in result.output
    
    def test_main_command_version(self):
        """测试版本信息"""
        result = self.runner.invoke(main, ['--version'])
        # 根据实际实现调整断言
        assert result.exit_code == 0 or "version" in result.output.lower()


class TestCLIIntegration:
    """CLI集成测试"""
    
    def setup_method(self):
        """测试前设置"""
        self.runner = CliRunner()
    
    @patch('zentao_mcp_client.proxy.start_proxy_server')
    def test_cli_workflow(self, mock_start_server):
        """测试完整的CLI工作流程"""
        with self.runner.isolated_filesystem():
            # 1. 检查初始状态
            with patch('zentao_mcp_client.cli.ClientConfig') as mock_config_class:
                mock_config = MagicMock()
                mock_config.is_configured.return_value = False
                mock_config.get_config_info.return_value = {
                    "config_file": "config.ini",
                    "backend_url": None,
                    "api_key_configured": False,
                    "api_key_preview": None
                }
                mock_config_class.return_value = mock_config
                
                result = self.runner.invoke(info)
                assert result.exit_code == 0
            
            # 2. 尝试启动未配置的客户端
            with patch('zentao_mcp_client.cli.ClientConfig') as mock_config_class:
                mock_config = MagicMock()
                mock_config.is_configured.return_value = False
                mock_config_class.return_value = mock_config
                
                result = self.runner.invoke(start)
                assert result.exit_code == 0
                assert "未配置" in result.output
            
            # 3. 配置客户端
            with patch('zentao_mcp_client.cli.ClientConfig') as mock_config_class:
                mock_config = MagicMock()
                mock_config.is_configured.return_value = False
                mock_config_class.return_value = mock_config
                
                result = self.runner.invoke(configure, input='http://test.com\ntest-key\n')
                assert result.exit_code == 0
            
            # 4. 检查配置后状态
            with patch('zentao_mcp_client.cli.ClientConfig') as mock_config_class:
                mock_config = MagicMock()
                mock_config.is_configured.return_value = True
                mock_config.get_config_info.return_value = {
                    "config_file": "config.ini",
                    "backend_url": "http://test.com",
                    "api_key_configured": True,
                    "api_key_preview": "test-***key"
                }
                mock_config_class.return_value = mock_config
                
                result = self.runner.invoke(info)
                assert result.exit_code == 0
                assert "http://test.com" in result.output


def test_cli_error_handling():
    """测试CLI错误处理"""
    runner = CliRunner()
    
    # 测试无效的命令
    result = runner.invoke(main, ['invalid-command'])
    assert result.exit_code != 0
    
    # 测试帮助信息
    result = runner.invoke(main, ['--help'])
    assert result.exit_code == 0
    assert "Usage:" in result.output
