"""
客户端配置管理模块
"""

import os
import configparser
from pathlib import Path
from typing import Optional


class ClientConfig:
    """客户端配置管理"""
    
    def __init__(self):
        self.config_dir = Path.home() / ".zentao_mcp_client"
        self.config_file = self.config_dir / "config.ini"
        self.config = configparser.ConfigParser()
        
        # 确保配置目录存在
        self.config_dir.mkdir(exist_ok=True)
        
        # 加载现有配置
        self.load_config()
    
    def load_config(self) -> None:
        """加载配置文件"""
        if self.config_file.exists():
            try:
                self.config.read(self.config_file)
            except configparser.Error:
                # 如果配置文件损坏，创建新的配置
                self.config = configparser.ConfigParser()

        # 确保有client section
        if not self.config.has_section('client'):
            self.config.add_section('client')
    
    def save_config(self) -> None:
        """保存配置文件"""
        with open(self.config_file, 'w') as f:
            self.config.write(f)
    
    def set_backend_url(self, url: Optional[str]) -> None:
        """设置后端服务URL"""
        if not self.config.has_section('client'):
            self.config.add_section('client')
        self.config.set('client', 'backend_url', url or '')
        self.save_config()

    def set_api_key(self, api_key: Optional[str]) -> None:
        """设置API Key"""
        if not self.config.has_section('client'):
            self.config.add_section('client')
        self.config.set('client', 'api_key', api_key or '')
        self.save_config()
    
    def get_backend_url(self) -> Optional[str]:
        """获取后端服务URL"""
        # 环境变量优先
        env_url = os.getenv('ZENTAO_MCP_BACKEND_URL') or os.getenv('ZENTAO_MCP_BACKEND')
        if env_url:
            return env_url.rstrip('/')
        # 本地配置
        try:
            url = self.config.get('client', 'backend_url')
            if url:
                return url.rstrip('/')
        except (configparser.NoSectionError, configparser.NoOptionError):
            pass
        # 开发环境回退：仓库内 config.ini
        try:
            repo_config_path = Path(__file__).resolve().parent.parent / 'config.ini'
            if repo_config_path.exists():
                tmp_cfg = configparser.ConfigParser()
                tmp_cfg.read(repo_config_path)
                url = tmp_cfg.get('client', 'backend_url', fallback='').strip()
                if url:
                    return url.rstrip('/')
        except Exception:
            pass
        return None

    def get_api_key(self) -> Optional[str]:
        """获取API Key"""
        # 环境变量优先
        env_key = os.getenv('ZENTAO_MCP_API_KEY')
        if env_key:
            return env_key.strip()
        # 本地配置
        try:
            key = self.config.get('client', 'api_key')
            if key:
                return key.strip()
        except (configparser.NoSectionError, configparser.NoOptionError):
            pass
        # 开发环境回退：仓库内 config.ini
        try:
            repo_config_path = Path(__file__).resolve().parent.parent / 'config.ini'
            if repo_config_path.exists():
                tmp_cfg = configparser.ConfigParser()
                tmp_cfg.read(repo_config_path)
                key = tmp_cfg.get('client', 'api_key', fallback='').strip()
                if key:
                    return key
        except Exception:
            pass
        return None
    
    def is_configured(self) -> bool:
        """检查是否已配置"""
        backend_url = self.get_backend_url()
        api_key = self.get_api_key()
        return bool(backend_url and api_key)
    
    def get_config_info(self) -> dict:
        """获取配置信息（不包含敏感信息），并标注来源(env/home/<USER>"""
        # 解析 backend_url 与来源
        source_backend = "none"
        backend_url = None
        env_url = os.getenv('ZENTAO_MCP_BACKEND_URL') or os.getenv('ZENTAO_MCP_BACKEND')
        if env_url:
            backend_url = env_url.rstrip('/')
            source_backend = "env"
        if backend_url is None:
            try:
                url = self.config.get('client', 'backend_url')
                if url:
                    backend_url = url.rstrip('/')
                    source_backend = "home"
            except Exception:
                pass
        if backend_url is None:
            try:
                repo_config_path = Path(__file__).resolve().parent.parent / 'config.ini'
                if repo_config_path.exists():
                    tmp_cfg = configparser.ConfigParser()
                    tmp_cfg.read(repo_config_path)
                    url = tmp_cfg.get('client', 'backend_url', fallback='').strip()
                    if url:
                        backend_url = url.rstrip('/')
                        source_backend = "repo"
            except Exception:
                pass

        # 解析 api_key 与来源
        source_api_key = "none"
        api_key = None
        env_key = os.getenv('ZENTAO_MCP_API_KEY')
        if env_key:
            api_key = env_key.strip()
            source_api_key = "env"
        if api_key is None:
            try:
                key = self.config.get('client', 'api_key')
                if key:
                    api_key = key.strip()
                    source_api_key = "home"
            except Exception:
                pass
        if api_key is None:
            try:
                repo_config_path = Path(__file__).resolve().parent.parent / 'config.ini'
                if repo_config_path.exists():
                    tmp_cfg = configparser.ConfigParser()
                    tmp_cfg.read(repo_config_path)
                    key = tmp_cfg.get('client', 'api_key', fallback='').strip()
                    if key:
                        api_key = key
                        source_api_key = "repo"
            except Exception:
                pass

        # API Key预览逻辑
        if api_key:
            if len(api_key) <= 3:
                api_key_preview = "***"
            elif len(api_key) <= 6:
                api_key_preview = f"{api_key[:3]}***"
            else:
                api_key_preview = f"{api_key[:2]}***{api_key[-2:]}"
        else:
            api_key_preview = 'Not configured'

        return {
            'config_file': str(self.config_file),
            'backend_url': backend_url,
            'backend_url_source': source_backend,
            'api_key_configured': bool(api_key),
            'api_key_preview': api_key_preview,
            'api_key_source': source_api_key,
        }