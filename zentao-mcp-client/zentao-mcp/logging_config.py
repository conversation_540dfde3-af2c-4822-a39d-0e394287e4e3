"""
客户端日志配置模块
"""

import logging
import logging.handlers
import json
import sys
from pathlib import Path
from typing import Optional, Dict, Any
from datetime import datetime


class JSONFormatter(logging.Formatter):
    """JSON格式化器"""
    
    def format(self, record: logging.LogRecord) -> str:
        """格式化日志记录为JSON"""
        
        # 基础日志信息
        log_data = {
            "timestamp": datetime.fromtimestamp(record.created).isoformat(),
            "level": record.levelname,
            "logger": record.name,
            "message": record.getMessage(),
            "module": record.module,
            "function": record.funcName,
            "line": record.lineno,
        }
        
        # 添加异常信息
        if record.exc_info:
            log_data["exception"] = self.formatException(record.exc_info)
        
        # 添加自定义字段
        if hasattr(record, 'request_id'):
            log_data["request_id"] = record.request_id
        
        if hasattr(record, 'tool_name'):
            log_data["tool_name"] = record.tool_name
            
        if hasattr(record, 'url'):
            log_data["url"] = record.url
            
        if hasattr(record, 'status_code'):
            log_data["status_code"] = record.status_code
            
        if hasattr(record, 'duration'):
            log_data["duration"] = record.duration
        
        return json.dumps(log_data, ensure_ascii=False)


class RequestLogFormatter(logging.Formatter):
    """请求日志专用格式化器"""
    
    def format(self, record: logging.LogRecord) -> str:
        """格式化请求日志"""
        
        if hasattr(record, 'request_data'):
            # 这是一个请求日志记录
            data = record.request_data
            
            lines = []
            lines.append("=" * 80)
            lines.append(f"🚀 [REQUEST] {data.get('tool_name', 'Unknown')}")
            lines.append(f"📍 URL: {data.get('url', 'Unknown')}")
            lines.append(f"🔧 Method: {data.get('method', 'Unknown')}")
            lines.append(f"⏰ Time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
            
            if data.get('headers'):
                lines.append("📋 Headers:")
                for key, value in data['headers'].items():
                    lines.append(f"    {key}: {value}")
            
            if data.get('request_body'):
                lines.append("📦 Request Body:")
                if isinstance(data['request_body'], dict):
                    lines.append(json.dumps(data['request_body'], indent=2, ensure_ascii=False))
                else:
                    lines.append(str(data['request_body']))
            else:
                lines.append("📦 Request Body: (empty)")
            
            lines.append("-" * 80)
            
            if data.get('response_status'):
                lines.append(f"📥 [RESPONSE] Status: {data['response_status']}")
                
                if data.get('response_headers'):
                    lines.append("📋 Response Headers:")
                    for key, value in data['response_headers'].items():
                        lines.append(f"    {key}: {value}")
                
                if data.get('response_body'):
                    lines.append("📦 Response Body:")
                    body = data['response_body']
                    if isinstance(body, dict):
                        body_str = json.dumps(body, indent=2, ensure_ascii=False)
                    else:
                        body_str = str(body)
                    
                    # 限制输出长度
                    if len(body_str) > 2000:
                        lines.append(body_str[:2000] + "\n... (truncated)")
                    else:
                        lines.append(body_str)
                
                if data.get('error'):
                    lines.append(f"❌ [ERROR] {data['error']}")
            
            lines.append("=" * 80)
            return "\n".join(lines)
        
        # 普通日志记录
        return super().format(record)


def setup_client_logging(
    log_level: str = "INFO",
    log_dir: Optional[Path] = None,
    enable_file_logging: bool = True,
    enable_request_logging: bool = True
) -> Dict[str, logging.Logger]:
    """
    设置客户端日志配置
    
    Args:
        log_level: 日志级别
        log_dir: 日志目录，如果为None则使用默认目录
        enable_file_logging: 是否启用文件日志
        enable_request_logging: 是否启用请求日志
        
    Returns:
        配置好的logger字典
    """
    
    # 确定日志目录
    if log_dir is None:
        log_dir = Path.home() / ".zentao_mcp_client" / "logs"
    
    log_dir.mkdir(parents=True, exist_ok=True)
    
    # 日志文件路径
    app_log_file = log_dir / "zentao_mcp_client.log"
    request_log_file = log_dir / "requests.log"
    
    # 清除现有的handlers
    for logger_name in ['zentao_mcp_client', 'zentao_mcp_client.requests']:
        logger = logging.getLogger(logger_name)
        logger.handlers.clear()
        logger.propagate = False
    
    # 控制台处理器
    console_handler = logging.StreamHandler(sys.stdout)
    console_handler.setLevel(log_level)
    console_handler.setFormatter(logging.Formatter(
        '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    ))
    
    # 应用日志logger
    app_logger = logging.getLogger('zentao_mcp_client')
    app_logger.setLevel(log_level)
    app_logger.addHandler(console_handler)
    
    # 请求日志logger
    request_logger = logging.getLogger('zentao_mcp_client.requests')
    request_logger.setLevel(logging.INFO)
    
    if enable_file_logging:
        # 应用日志文件处理器
        app_file_handler = logging.handlers.RotatingFileHandler(
            app_log_file,
            maxBytes=10 * 1024 * 1024,  # 10MB
            backupCount=5,
            encoding='utf-8'
        )
        app_file_handler.setLevel(log_level)
        app_file_handler.setFormatter(JSONFormatter())
        app_logger.addHandler(app_file_handler)
        
        if enable_request_logging:
            # 请求日志文件处理器
            request_file_handler = logging.handlers.RotatingFileHandler(
                request_log_file,
                maxBytes=50 * 1024 * 1024,  # 50MB
                backupCount=10,
                encoding='utf-8'
            )
            request_file_handler.setLevel(logging.INFO)
            request_file_handler.setFormatter(RequestLogFormatter())
            request_logger.addHandler(request_file_handler)
    
    return {
        'app': app_logger,
        'requests': request_logger,
        'log_dir': log_dir,
        'app_log_file': app_log_file,
        'request_log_file': request_log_file
    }


def get_client_logger(name: str = 'zentao_mcp_client') -> logging.Logger:
    """获取客户端logger"""
    return logging.getLogger(name)


def get_request_logger() -> logging.Logger:
    """获取请求logger"""
    return logging.getLogger('zentao_mcp_client.requests')


def log_request(
    tool_name: str,
    url: str,
    method: str,
    headers: Dict[str, str],
    request_body: Any = None,
    response_status: Optional[int] = None,
    response_headers: Optional[Dict[str, str]] = None,
    response_body: Any = None,
    error: Optional[str] = None
) -> None:
    """
    记录请求日志
    
    Args:
        tool_name: 工具名称
        url: 请求URL
        method: HTTP方法
        headers: 请求头
        request_body: 请求体
        response_status: 响应状态码
        response_headers: 响应头
        response_body: 响应体
        error: 错误信息
    """
    
    request_logger = get_request_logger()
    
    # 构建请求数据
    request_data = {
        'tool_name': tool_name,
        'url': url,
        'method': method,
        'headers': headers,
        'request_body': request_body,
        'response_status': response_status,
        'response_headers': dict(response_headers) if response_headers else None,
        'response_body': response_body,
        'error': error
    }
    
    # 创建日志记录
    record = logging.LogRecord(
        name='zentao_mcp_client.requests',
        level=logging.INFO,
        pathname='',
        lineno=0,
        msg='Request log',
        args=(),
        exc_info=None
    )
    record.request_data = request_data
    
    request_logger.handle(record)
