"""
日志查看工具
"""

import json
import click
from pathlib import Path
from typing import Optional, List, Dict, Any
from datetime import datetime, timedelta


class LogViewer:
    """日志查看器"""
    
    def __init__(self, log_dir: Optional[Path] = None):
        if log_dir is None:
            self.log_dir = Path.home() / ".zentao_mcp_client" / "logs"
        else:
            self.log_dir = Path(log_dir)
        
        self.app_log_file = self.log_dir / "zentao_mcp_client.log"
        self.request_log_file = self.log_dir / "requests.log"
    
    def get_log_files_info(self) -> Dict[str, Any]:
        """获取日志文件信息"""
        info = {
            "log_dir": str(self.log_dir),
            "log_dir_exists": self.log_dir.exists(),
            "files": {}
        }
        
        for log_file, name in [(self.app_log_file, "app"), (self.request_log_file, "requests")]:
            if log_file.exists():
                stat = log_file.stat()
                info["files"][name] = {
                    "path": str(log_file),
                    "size": stat.st_size,
                    "size_mb": round(stat.st_size / 1024 / 1024, 2),
                    "modified": datetime.fromtimestamp(stat.st_mtime).strftime("%Y-%m-%d %H:%M:%S")
                }
            else:
                info["files"][name] = {
                    "path": str(log_file),
                    "exists": False
                }
        
        return info
    
    def read_app_logs(self, lines: int = 50, level: Optional[str] = None) -> List[Dict[str, Any]]:
        """读取应用日志"""
        if not self.app_log_file.exists():
            return []
        
        logs = []
        try:
            with open(self.app_log_file, 'r', encoding='utf-8') as f:
                for line in f:
                    line = line.strip()
                    if not line:
                        continue
                    
                    try:
                        log_data = json.loads(line)
                        if level and log_data.get('level') != level.upper():
                            continue
                        logs.append(log_data)
                    except json.JSONDecodeError:
                        # 跳过无效的JSON行
                        continue
        except Exception as e:
            click.echo(f"读取应用日志失败: {e}")
            return []
        
        # 返回最后N行
        return logs[-lines:] if lines > 0 else logs
    
    def read_request_logs(self, lines: int = 10) -> List[str]:
        """读取请求日志"""
        if not self.request_log_file.exists():
            return []
        
        try:
            with open(self.request_log_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 按分隔符分割请求
            requests = content.split("=" * 80)
            requests = [req.strip() for req in requests if req.strip()]
            
            # 返回最后N个请求
            return requests[-lines:] if lines > 0 else requests
            
        except Exception as e:
            click.echo(f"读取请求日志失败: {e}")
            return []
    
    def search_logs(self, keyword: str, log_type: str = "app", lines: int = 20) -> List[Any]:
        """搜索日志"""
        if log_type == "app":
            logs = self.read_app_logs(lines=0)  # 读取所有日志
            return [log for log in logs if keyword.lower() in log.get('message', '').lower()]
        elif log_type == "requests":
            requests = self.read_request_logs(lines=0)  # 读取所有请求
            return [req for req in requests if keyword.lower() in req.lower()]
        else:
            return []
    
    def get_recent_errors(self, hours: int = 24) -> List[Dict[str, Any]]:
        """获取最近的错误日志"""
        cutoff_time = datetime.now() - timedelta(hours=hours)
        logs = self.read_app_logs(lines=0)
        
        error_logs = []
        for log in logs:
            try:
                log_time = datetime.fromisoformat(log.get('timestamp', ''))
                if log_time >= cutoff_time and log.get('level') in ['ERROR', 'CRITICAL']:
                    error_logs.append(log)
            except:
                continue
        
        return error_logs
    
    def clear_logs(self, log_type: str = "all") -> bool:
        """清理日志文件"""
        try:
            if log_type in ["all", "app"] and self.app_log_file.exists():
                self.app_log_file.unlink()
                click.echo(f"已清理应用日志: {self.app_log_file}")
            
            if log_type in ["all", "requests"] and self.request_log_file.exists():
                self.request_log_file.unlink()
                click.echo(f"已清理请求日志: {self.request_log_file}")
            
            return True
        except Exception as e:
            click.echo(f"清理日志失败: {e}")
            return False


@click.group()
def logs():
    """日志管理命令"""
    pass


@logs.command()
def info():
    """显示日志文件信息"""
    viewer = LogViewer()
    info = viewer.get_log_files_info()
    
    click.echo("=== Zentao MCP Client 日志信息 ===")
    click.echo(f"日志目录: {info['log_dir']}")
    click.echo(f"目录存在: {'是' if info['log_dir_exists'] else '否'}")
    click.echo()
    
    for name, file_info in info['files'].items():
        click.echo(f"{name.upper()} 日志:")
        if file_info.get('exists', True):
            click.echo(f"  路径: {file_info['path']}")
            click.echo(f"  大小: {file_info['size_mb']} MB")
            click.echo(f"  修改时间: {file_info['modified']}")
        else:
            click.echo(f"  路径: {file_info['path']}")
            click.echo(f"  状态: 文件不存在")
        click.echo()


@logs.command()
@click.option('--lines', '-n', default=50, help='显示行数')
@click.option('--level', help='过滤日志级别 (DEBUG, INFO, WARNING, ERROR, CRITICAL)')
def app(lines: int, level: Optional[str]):
    """查看应用日志"""
    viewer = LogViewer()
    logs_data = viewer.read_app_logs(lines=lines, level=level)
    
    if not logs_data:
        click.echo("没有找到应用日志")
        return
    
    click.echo(f"=== 应用日志 (最近 {len(logs_data)} 条) ===")
    for log in logs_data:
        timestamp = log.get('timestamp', 'Unknown')
        level_str = log.get('level', 'INFO')
        message = log.get('message', '')
        module = log.get('module', '')
        
        # 颜色编码
        if level_str == 'ERROR':
            level_color = click.style(level_str, fg='red')
        elif level_str == 'WARNING':
            level_color = click.style(level_str, fg='yellow')
        elif level_str == 'INFO':
            level_color = click.style(level_str, fg='green')
        else:
            level_color = level_str
        
        click.echo(f"[{timestamp}] {level_color} {module}: {message}")


@logs.command()
@click.option('--lines', '-n', default=10, help='显示请求数')
def requests(lines: int):
    """查看请求日志"""
    viewer = LogViewer()
    requests_data = viewer.read_request_logs(lines=lines)
    
    if not requests_data:
        click.echo("没有找到请求日志")
        return
    
    click.echo(f"=== 请求日志 (最近 {len(requests_data)} 个请求) ===")
    for i, request in enumerate(requests_data, 1):
        click.echo(f"\n--- 请求 {i} ---")
        click.echo(request)


@logs.command()
@click.option('--keyword', '-k', required=True, help='搜索关键词')
@click.option('--type', '-t', default='app', type=click.Choice(['app', 'requests']), help='日志类型')
@click.option('--lines', '-n', default=20, help='最大结果数')
def search(keyword: str, type: str, lines: int):
    """搜索日志"""
    viewer = LogViewer()
    results = viewer.search_logs(keyword=keyword, log_type=type, lines=lines)
    
    if not results:
        click.echo(f"没有找到包含 '{keyword}' 的{type}日志")
        return
    
    click.echo(f"=== 搜索结果: '{keyword}' ({len(results)} 条) ===")
    for result in results:
        if type == 'app':
            timestamp = result.get('timestamp', 'Unknown')
            level_str = result.get('level', 'INFO')
            message = result.get('message', '')
            click.echo(f"[{timestamp}] {level_str}: {message}")
        else:
            click.echo(result)
            click.echo("-" * 40)


@logs.command()
@click.option('--hours', '-h', default=24, help='时间范围(小时)')
def errors(hours: int):
    """查看最近的错误日志"""
    viewer = LogViewer()
    error_logs = viewer.get_recent_errors(hours=hours)
    
    if not error_logs:
        click.echo(f"最近 {hours} 小时内没有错误日志")
        return
    
    click.echo(f"=== 最近 {hours} 小时的错误日志 ({len(error_logs)} 条) ===")
    for log in error_logs:
        timestamp = log.get('timestamp', 'Unknown')
        level_str = log.get('level', 'ERROR')
        message = log.get('message', '')
        module = log.get('module', '')
        
        level_color = click.style(level_str, fg='red')
        click.echo(f"[{timestamp}] {level_color} {module}: {message}")


@logs.command()
@click.option('--type', '-t', default='all', type=click.Choice(['all', 'app', 'requests']), help='清理的日志类型')
@click.confirmation_option(prompt='确定要清理日志文件吗？')
def clear(type: str):
    """清理日志文件"""
    viewer = LogViewer()
    if viewer.clear_logs(log_type=type):
        click.echo("日志清理完成")
    else:
        click.echo("日志清理失败")


if __name__ == '__main__':
    logs()
