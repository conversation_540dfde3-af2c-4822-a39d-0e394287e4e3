"""
核心代理逻辑模块 - 使用FastMCP SDK
"""

import asyncio
import logging
import httpx
from typing import Any, Dict, List, Optional
from fastmcp import FastMCP
from starlette.requests import Request
from starlette.responses import JSONResponse
from zentao_mcp.config import ClientConfig
from zentao_mcp.logging_config import setup_client_logging, log_request, get_client_logger

logger = get_client_logger(__name__)


class ZentaoMCPProxy:
    """Zentao MCP代理服务"""

    def __init__(self, config: ClientConfig, enable_file_logging: bool = True):
        self.config = config
        self.backend_url = config.get_backend_url()
        self.api_key = config.get_api_key()
        self.client = httpx.AsyncClient(timeout=30.0)

        # 设置日志系统
        self.loggers = setup_client_logging(
            log_level="INFO",
            enable_file_logging=enable_file_logging,
            enable_request_logging=True
        )

        logger.info(f"Zentao MCP代理初始化完成")
        logger.info(f"后端服务: {self.backend_url}")
        logger.info(f"日志目录: {self.loggers['log_dir']}")
        if enable_file_logging:
            logger.info(f"应用日志: {self.loggers['app_log_file']}")
            logger.info(f"请求日志: {self.loggers['request_log_file']}")

        # 工具端点映射表 - 修复API不匹配问题
        self.tool_endpoints = {
            # 部门相关工具
            "zentao_get_all_departments": "/api/v1/zentao/departments/zentao_get_all_departments",
            "zentao_get_users_by_dept": "/api/v1/zentao/departments/zentao_get_users_by_dept",

            # 项目相关工具
            "zentao_get_all_projects": "/api/v1/zentao/projects/zentao_get_all_projects",
            "zentao_get_stories_by_project": "/api/v1/zentao/projects/zentao_get_stories_by_project",
            "zentao_get_tasks_by_project": "/api/v1/zentao/projects/zentao_get_tasks_by_project",
            "zentao_get_bugs_by_project": "/api/v1/zentao/projects/zentao_get_bugs_by_project",

            # 任务相关工具
            "zentao_get_task_detail": "/api/v1/zentao/tasks/zentao_get_task_detail",
            "zentao_get_tasks_by_account": "/api/v1/zentao/tasks/zentao_get_tasks_by_account",
            "zentao_get_tasks_by_dept": "/api/v1/zentao/tasks/zentao_get_tasks_by_dept",

            # 需求相关工具
            "zentao_get_story_detail": "/api/v1/zentao/stories/zentao_get_story_detail",
            "zentao_get_story_info": "/api/v1/zentao/stories/zentao_get_story_info",
            "zentao_get_stories_end_info": "/api/v1/zentao/stories/zentao_get_stories_end_info",
            "zentao_check_story_exists": "/api/v1/zentao/stories/zentao_check_story_exists",
            "zentao_get_stories_by_time": "/api/v1/zentao/stories/zentao_get_stories_by_time",

            # Bug相关工具
            "zentao_get_bugs_by_time_range": "/api/v1/zentao/bugs/zentao_get_bugs_by_time_range",
            "zentao_get_bug_detail": "/api/v1/zentao/bugs/zentao_get_bug_detail",
            "zentao_get_personal_bugs": "/api/v1/zentao/bugs/zentao_get_personal_bugs",
            "zentao_get_bugs_by_time_and_dept": "/api/v1/zentao/bugs/zentao_get_bugs_by_time_and_dept",

            # 用户相关工具
            "zentao_get_users_by_accounts": "/api/v1/zentao/users/zentao_get_users_by_accounts",

            # 分析相关工具
            "zentao_get_project_analysis": "/api/v1/zentao/analysis/zentao_get_project_analysis",
            "analyze_story_workload": "/api/v1/zentao/analysis/analyze_story_workload",
            "analyze_bugs_by_dept_and_time": "/api/v1/zentao/analysis/analyze_bugs_by_dept_and_time",
            "project_summary_analysis": "/api/v1/zentao/analysis/project_summary_analysis",
            "personnel_workload_analysis": "/api/v1/zentao/analysis/personnel_workload_analysis",
            "story_task_relation_query": "/api/v1/zentao/analysis/story_task_relation_query",
            "bug_to_story_tracking": "/api/v1/zentao/analysis/bug_to_story_tracking",
        }

        # 初始化FastMCP
        self.mcp = FastMCP("Zentao MCP Client")

        # 注册工具
        self._register_tools()

        # 注册资源
        self._register_resources()

        # 注册自定义路由
        self._register_custom_routes()

    def _register_tools(self):
        """注册所有工具"""

        # 部门相关工具
        @self.mcp.tool()
        async def zentao_get_all_departments() -> Dict[str, Any]:
            """获取所有部门列表"""
            return await self._forward_request("zentao_get_all_departments", {})

        @self.mcp.tool()
        async def zentao_get_users_by_dept(dept_id: int) -> Dict[str, Any]:
            """根据部门ID获取用户列表"""
            return await self._forward_request("zentao_get_users_by_dept", {"dept_id": dept_id})

        # 项目相关工具
        @self.mcp.tool()
        async def zentao_get_all_projects() -> Dict[str, Any]:
            """获取所有项目列表"""
            return await self._forward_request("zentao_get_all_projects", {})

        @self.mcp.tool()
        async def zentao_get_stories_by_project(project_id: int) -> Dict[str, Any]:
            """根据项目ID获取需求列表"""
            return await self._forward_request("zentao_get_stories_by_project", {"project_id": project_id})

        @self.mcp.tool()
        async def zentao_get_tasks_by_project(project_id: int) -> Dict[str, Any]:
            """根据项目ID获取任务列表"""
            return await self._forward_request("zentao_get_tasks_by_project", {"project_id": project_id})

        @self.mcp.tool()
        async def zentao_get_bugs_by_project(project_id: int) -> Dict[str, Any]:
            """根据项目ID获取Bug列表"""
            return await self._forward_request("zentao_get_bugs_by_project", {"project_id": project_id})

        # Bug相关工具
        @self.mcp.tool()
        async def zentao_get_bugs_by_time_and_dept(start_date: str, end_date: str, dept_id: int) -> Dict[str, Any]:
            """根据时间范围和部门查询Bug"""
            return await self._forward_request("zentao_get_bugs_by_time_and_dept", {
                "start_date": start_date,
                "end_date": end_date,
                "dept_id": dept_id
            })

        @self.mcp.tool()
        async def zentao_get_bug_detail(bug_id: int) -> Dict[str, Any]:
            """获取Bug详情"""
            return await self._forward_request("zentao_get_bug_detail", {"bug_id": bug_id})

        @self.mcp.tool()
        async def zentao_get_bugs_by_time_range(start_date: str, end_date: str, dept_id: Optional[int] = None) -> Dict[
            str, Any]:
            """根据时间范围查询Bug"""
            return await self._forward_request("zentao_get_bugs_by_time_range", {
                "start_date": start_date,
                "end_date": end_date,
                "dept_id": dept_id
            })

        @self.mcp.tool()
        async def zentao_get_personal_bugs(account: str, status: str, start_date: str, end_date: str) -> Dict[str, Any]:
            """查询个人Bug"""
            return await self._forward_request("zentao_get_personal_bugs", {
                "account": account,
                "status": status,
                "start_date": start_date,
                "end_date": end_date
            })

        # 需求相关工具
        @self.mcp.tool()
        async def zentao_get_story_detail(story_id: int) -> Dict[str, Any]:
            """获取需求详情"""
            return await self._forward_request("zentao_get_story_detail", {"story_id": story_id})

        @self.mcp.tool()
        async def zentao_get_story_info(story_ids: List[str]) -> Dict[str, Any]:
            """获取需求信息（工时）"""
            return await self._forward_request("zentao_get_story_info", {"story_ids": story_ids})

        @self.mcp.tool()
        async def zentao_get_stories_end_info(story_ids: List[str]) -> Dict[str, Any]:
            """获取需求完成信息"""
            return await self._forward_request("zentao_get_stories_end_info", {"story_ids": story_ids})

        @self.mcp.tool()
        async def zentao_check_story_exists(story_ids: List[str]) -> Dict[str, Any]:
            """检查需求是否存在"""
            return await self._forward_request("zentao_check_story_exists", {"story_ids": story_ids})

        @self.mcp.tool()
        async def zentao_get_stories_by_time(status: str, start_date: str, end_date: str) -> Dict[str, Any]:
            """根据时间段查询需求"""
            return await self._forward_request("zentao_get_stories_by_time", {
                "status": status,
                "start_date": start_date,
                "end_date": end_date
            })

        # 任务相关工具
        @self.mcp.tool()
        async def zentao_get_task_detail(task_id: int) -> Dict[str, Any]:
            """获取任务详情"""
            return await self._forward_request("zentao_get_task_detail", {"task_id": task_id})

        @self.mcp.tool()
        async def zentao_get_tasks_by_account(account: str, start_date: str, end_date: str, is_doing: bool = False) -> \
        Dict[str, Any]:
            """根据域账号查询任务"""
            return await self._forward_request("zentao_get_tasks_by_account", {
                "account": account,
                "start_date": start_date,
                "end_date": end_date,
                "is_doing": is_doing
            })

        @self.mcp.tool()
        async def zentao_get_tasks_by_dept(dept_id: int, start_date: str, end_date: str) -> Dict[str, Any]:
            """根据部门查询任务"""
            return await self._forward_request("zentao_get_tasks_by_dept", {
                "dept_id": dept_id,
                "start_date": start_date,
                "end_date": end_date
            })

        # 用户相关工具
        @self.mcp.tool()
        async def zentao_get_users_by_accounts(accounts: List[str]) -> Dict[str, Any]:
            """根据账号列表获取用户信息"""
            return await self._forward_request("zentao_get_users_by_accounts", {"accounts": accounts})

        # 分析相关工具
        @self.mcp.tool()
        async def zentao_get_project_analysis(project_id: int, start_date: str, end_date: str) -> Dict[str, Any]:
            """获取项目分析数据"""
            return await self._forward_request("zentao_get_project_analysis", {
                "project_id": project_id,
                "start_date": start_date,
                "end_date": end_date
            })

        @self.mcp.tool()
        async def analyze_story_workload(story_ids: Optional[List[int]] = None, project_id: Optional[int] = None,
                                         include_completed: bool = True) -> Dict[str, Any]:
            """分析需求工作量"""
            return await self._forward_request("analyze_story_workload", {
                "story_ids": story_ids,
                "project_id": project_id,
                "include_completed": include_completed
            })

        @self.mcp.tool()
        async def analyze_bugs_by_dept_and_time(dept_id: int, start_date: str, end_date: str) -> Dict[str, Any]:
            """按部门和时间分析Bug"""
            return await self._forward_request("analyze_bugs_by_dept_and_time", {
                "dept_id": dept_id,
                "start_date": start_date,
                "end_date": end_date
            })

        @self.mcp.tool()
        async def project_summary_analysis(project_id: int) -> Dict[str, Any]:
            """项目汇总分析"""
            return await self._forward_request("project_summary_analysis", {"project_id": project_id})

        @self.mcp.tool()
        async def personnel_workload_analysis(accounts: List[str], start_date: str, end_date: str) -> Dict[str, Any]:
            """人员工作量分析"""
            return await self._forward_request("personnel_workload_analysis", {
                "accounts": accounts,
                "start_date": start_date,
                "end_date": end_date
            })

        @self.mcp.tool()
        async def story_task_relation_query(story_ids: Optional[List[int]] = None, project_id: Optional[int] = None) -> \
        Dict[str, Any]:
            """需求-任务关联查询"""
            return await self._forward_request("story_task_relation_query", {
                "story_ids": story_ids,
                "project_id": project_id
            })

        @self.mcp.tool()
        async def bug_to_story_tracking(bug_ids: List[int]) -> Dict[str, Any]:
            """Bug到需求追踪"""
            return await self._forward_request("bug_to_story_tracking", {"bug_ids": bug_ids})

    def _register_resources(self):
        """注册资源"""

        @self.mcp.resource("zentao://departments")
        async def departments_resource() -> str:
            """部门资源"""
            result = await self._forward_request("zentao_get_all_departments", {})
            return str(result)

    def _register_custom_routes(self):
        """注册自定义HTTP路由"""

        @self.mcp.custom_route("/health", methods=["GET"])
        async def health_check(request: Request) -> JSONResponse:
            """健康检查端点"""
            return JSONResponse({
                "status": "healthy",
                "service": "zentao-mcp-client",
                "backend_url": self.backend_url
            })

        @self.mcp.resource("zentao://projects")
        async def projects_resource() -> str:
            """项目资源"""
            result = await self._forward_request("zentao_get_all_projects", {})
            return str(result)

    async def _forward_request(self, tool_name: str, arguments: Dict[str, Any]) -> Any:
        """转发请求到后端服务 - 修复后的版本，包含详细请求日志"""
        try:
            # 获取对应的端点
            endpoint = self.tool_endpoints.get(tool_name)
            if not endpoint:
                error_msg = f"不支持的工具: {tool_name}"
                logger.error(error_msg)
                raise Exception(error_msg)

            # 构建请求头
            headers = {
                "Authorization": f"Bearer {self.api_key}",
                "Content-Type": "application/json"
            }

            # 构建完整URL
            full_url = f"{self.backend_url}{endpoint}"

            # 记录请求开始
            logger.info(f"开始处理工具调用: {tool_name}")

            # 打印详细的请求信息到控制台
            print("=" * 80)
            print(f"🚀 [REQUEST] {tool_name}")
            print(f"📍 URL: {full_url}")
            print(f"🔧 Method: POST")
            print(f"📋 Headers:")
            for key, value in headers.items():
                if key.lower() == "authorization":
                    # 显示完整的API Key，不保护隐私
                    print(f"    {key}: {value}")
                else:
                    print(f"    {key}: {value}")
            print(f"📦 Request Body:")
            if arguments:
                import json
                print(json.dumps(arguments, indent=2, ensure_ascii=False))
            else:
                print("    (empty)")
            print("-" * 80)

            # 发送请求到具体端点
            response = await self.client.post(
                full_url,
                json=arguments,  # 直接传递参数，不包装
                headers=headers
            )

            # 打印响应信息到控制台
            print(f"📥 [RESPONSE] Status: {response.status_code}")
            print(f"📋 Response Headers:")
            for key, value in response.headers.items():
                print(f"    {key}: {value}")

            response.raise_for_status()
            result = response.json()

            print(f"📦 Response Body:")
            if result:
                import json
                # 限制输出长度，避免过长的响应
                result_str = json.dumps(result, indent=2, ensure_ascii=False)
                if len(result_str) > 2000:
                    print(result_str[:2000] + "\n... (truncated)")
                else:
                    print(result_str)
            else:
                print("    (empty)")
            print("=" * 80)

            # 记录成功的请求日志到文件
            log_request(
                tool_name=tool_name,
                url=full_url,
                method="POST",
                headers=headers,
                request_body=arguments,
                response_status=response.status_code,
                response_headers=dict(response.headers),
                response_body=result
            )

            logger.info(f"工具调用成功: {tool_name}, 状态码: {response.status_code}")

            # 直接返回结果，后端已经是标准格式
            return result

        except httpx.HTTPStatusError as e:
            error_msg = f"HTTP请求失败: {e.response.status_code}"

            print(f"❌ [ERROR] {error_msg}")
            print(f"📋 Error Response Headers:")
            for key, value in e.response.headers.items():
                print(f"    {key}: {value}")
            print(f"📦 Error Response Body:")
            print(e.response.text)
            print("=" * 80)

            # 记录错误的请求日志到文件
            log_request(
                tool_name=tool_name,
                url=full_url,
                method="POST",
                headers=headers,
                request_body=arguments,
                response_status=e.response.status_code,
                response_headers=dict(e.response.headers),
                response_body=e.response.text,
                error=error_msg
            )

            logger.error(f"工具调用失败: {tool_name}, {error_msg} - {e.response.text}")

            # 尝试解析错误响应
            try:
                error_detail = e.response.json()
                raise Exception(f"后端服务错误: {error_detail.get('detail', '未知错误')}")
            except:
                raise Exception(f"后端服务错误: {e.response.status_code}")

        except httpx.ConnectError as e:
            error_msg = f"连接失败: {e}"
            print(f"❌ [ERROR] {error_msg}")
            print("=" * 80)

            # 记录连接错误日志
            log_request(
                tool_name=tool_name,
                url=full_url,
                method="POST",
                headers=headers,
                request_body=arguments,
                error=error_msg
            )

            logger.error(f"工具调用连接失败: {tool_name}, {error_msg}")
            raise Exception(f"转发请求失败: Connection failed")

        except Exception as e:
            error_msg = f"请求异常: {e}"
            print(f"❌ [ERROR] {error_msg}")
            print("=" * 80)

            # 记录一般异常日志
            log_request(
                tool_name=tool_name,
                url=full_url if 'full_url' in locals() else "Unknown",
                method="POST",
                headers=headers if 'headers' in locals() else {},
                request_body=arguments,
                error=error_msg
            )

            logger.error(f"工具调用异常: {tool_name}, {error_msg}")
            raise

    async def close(self):
        """关闭代理服务"""
        await self.client.aclose()


def start_proxy_server(host: str, port: int, config: ClientConfig, mode: str = "stdio"):
    """启动代理服务器 - 支持多种模式"""

    proxy = ZentaoMCPProxy(config)
    try:
        if mode == "http":
            # HTTP服务器模式（使用FastMCP内置HTTP服务器）
            proxy.mcp.run(transport="http", host=host, port=port)

        elif mode == "stdio":
            # STDIO模式（FastMCP内部使用anyio.run管理事件循环，外层不可再套 asyncio.run）
            logger.info("启动STDIO模式...")
            proxy.mcp.run(transport="stdio")
        elif mode == "sse":
            # SSE模式暂未完全实现，退回STDIO
            logger.warning("SSE模式暂未完全实现，使用STDIO模式")
            proxy.mcp.run(transport="sse", port=port)
        else:
            raise ValueError(f"不支持的模式: {mode}")
    except KeyboardInterrupt:
        logger.info("服务已停止")
    finally:
        # 关闭HTTP异步客户端
        try:
            asyncio.run(proxy.close())
        except RuntimeError:
            # 若当前线程已有事件循环或已关闭，忽略
            pass
