#!/bin/bash
set -euo pipefail

# ============================================================================
# IDE集成配置脚本
# 自动配置Cursor、Claude Desktop等IDE的MCP集成
# ============================================================================

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 显示帮助信息
show_help() {
    cat << 'EOF'
IDE集成配置脚本

用法: ./configure-ide-integration.sh [选项] [IDE]

IDE选项:
  cursor        配置Cursor IDE
  claude        配置Claude Desktop
  all           配置所有支持的IDE (默认)

选项:
  --backend-url URL     后端服务URL (默认: http://localhost:8000)
  --api-key KEY         API Key (默认: 自动检测)
  --mode MODE           客户端模式 (stdio|http) (默认: stdio)
  -v, --verbose         详细输出
  -h, --help            显示帮助信息

示例:
  ./configure-ide-integration.sh                           # 配置所有IDE
  ./configure-ide-integration.sh cursor                    # 仅配置Cursor
  ./configure-ide-integration.sh --api-key=your-key        # 指定API Key
  ./configure-ide-integration.sh --backend-url=https://api.example.com  # 指定后端URL
EOF
}

# 默认参数
TARGET_IDE="all"
BACKEND_URL="http://localhost:8000"
API_KEY=""
CLIENT_MODE="stdio"
VERBOSE=false

# 获取API Key
get_api_key() {
    local api_key=""
    
    # 如果已指定API Key，直接使用
    if [[ -n "$API_KEY" ]]; then
        echo "$API_KEY"
        return 0
    fi
    
    # 尝试从环境变量获取
    if [[ -n "${ZENTAO_MCP_API_KEY:-}" ]]; then
        api_key="$ZENTAO_MCP_API_KEY"
        log_info "从环境变量获取API Key"
    else
        # 尝试从配置文件获取
        local config_files=(
            "zentao-mcp-client/config.ini"
            "$HOME/.zentao_mcp_client/config.ini"
            "zentao-mcp-client/config/client.dev.ini"
        )
        
        for config_file in "${config_files[@]}"; do
            if [[ -f "$config_file" ]]; then
                api_key=$(grep "^api_key" "$config_file" 2>/dev/null | cut -d'=' -f2 | tr -d ' "' || true)
                if [[ -n "$api_key" && "$api_key" != "your-api-key-here" ]]; then
                    log_info "从配置文件获取API Key: $config_file"
                    break
                fi
            fi
        done
    fi
    
    if [[ -z "$api_key" || "$api_key" == "your-api-key-here" ]]; then
        log_warning "未找到有效的API Key，使用占位符"
        api_key="your-api-key-here"
    fi
    
    echo "$api_key"
}

# 检查zentao-mcp-client是否可用
check_client_availability() {
    log_info "检查zentao-mcp-client可用性..."
    
    # 检查是否已安装
    if command -v zentao-mcp-client &> /dev/null; then
        log_success "zentao-mcp-client已安装"
        return 0
    fi
    
    # 检查源码是否存在
    if [[ -d "zentao-mcp-client" && -f "zentao-mcp-client/pyproject.toml" ]]; then
        log_info "找到zentao-mcp-client源码"
        return 0
    fi
    
    log_error "未找到zentao-mcp-client"
    log_error "请先运行: ./deploy-client-universal.sh dev deploy"
    exit 1
}

# 获取客户端命令
get_client_command() {
    if command -v zentao-mcp &> /dev/null; then
        echo "zentao-mcp"
    elif [[ -d "zentao-mcp-client" ]]; then
        echo "uv"
    else
        echo "zentao-mcp"
    fi
}

# 获取客户端参数
get_client_args() {
    local cmd=$(get_client_command)
    
    if [[ "$cmd" == "uv" ]]; then
        echo '["run", "python", "-m", "zentao_mcp", "start", "--mode", "'$CLIENT_MODE'"]'
    else
        if [[ "$CLIENT_MODE" == "stdio" ]]; then
            echo '["start"]'
        else
            echo '["start", "--mode", "'$CLIENT_MODE'"]'
        fi
    fi
}

# 获取工作目录
get_working_directory() {
    local cmd=$(get_client_command)
    
    if [[ "$cmd" == "uv" ]]; then
        echo "$(pwd)/zentao-mcp-client"
    else
        echo null
    fi
}

# 配置Cursor IDE
configure_cursor() {
    log_info "配置Cursor IDE集成..."
    
    local cursor_config_dir=""
    case "$(uname)" in
        Darwin)
            cursor_config_dir="$HOME/Library/Application Support/Cursor/User/globalStorage/rooveterinaryinc.roo-cline/settings"
            ;;
        Linux)
            cursor_config_dir="$HOME/.config/Cursor/User/globalStorage/rooveterinaryinc.roo-cline/settings"
            ;;
        CYGWIN*|MINGW*|MSYS*)
            cursor_config_dir="$APPDATA/Cursor/User/globalStorage/rooveterinaryinc.roo-cline/settings"
            ;;
    esac
    
    if [[ -z "$cursor_config_dir" ]]; then
        log_warning "无法确定Cursor配置目录"
        return 1
    fi
    
    mkdir -p "$cursor_config_dir"
    
    local api_key=$(get_api_key)
    local client_cmd=$(get_client_command)
    local client_args=$(get_client_args)
    local working_dir=$(get_working_directory)
    
    local config_content='{
  "mcpServers": {
    "zentao-mcp": {
      "command": "'$client_cmd'",
      "args": '$client_args','
    
    if [[ "$working_dir" != "null" ]]; then
        config_content+='"cwd": "'$working_dir'",'
    fi
    
    config_content+='
      "env": {
        "ZENTAO_MCP_BACKEND_URL": "'$BACKEND_URL'",
        "ZENTAO_MCP_API_KEY": "'$api_key'"
      }
    }
  }
}'
    
    echo "$config_content" > "$cursor_config_dir/cline_mcp_settings.json"
    
    log_success "Cursor配置已创建: $cursor_config_dir/cline_mcp_settings.json"
    
    if [[ "$VERBOSE" == "true" ]]; then
        log_info "配置内容:"
        cat "$cursor_config_dir/cline_mcp_settings.json"
    fi
}

# 配置Claude Desktop
configure_claude() {
    log_info "配置Claude Desktop集成..."
    
    local claude_config_dir=""
    case "$(uname)" in
        Darwin)
            claude_config_dir="$HOME/Library/Application Support/Claude"
            ;;
        Linux)
            claude_config_dir="$HOME/.config/Claude"
            ;;
        CYGWIN*|MINGW*|MSYS*)
            claude_config_dir="$APPDATA/Claude"
            ;;
    esac
    
    if [[ -z "$claude_config_dir" ]]; then
        log_warning "无法确定Claude Desktop配置目录"
        return 1
    fi
    
    mkdir -p "$claude_config_dir"
    
    local api_key=$(get_api_key)
    local client_cmd=$(get_client_command)
    local client_args=$(get_client_args)
    local working_dir=$(get_working_directory)
    
    local config_content='{
  "mcpServers": {
    "zentao-mcp": {
      "command": "'$client_cmd'",
      "args": '$client_args','
    
    if [[ "$working_dir" != "null" ]]; then
        config_content+='"cwd": "'$working_dir'",'
    fi
    
    config_content+='
      "env": {
        "ZENTAO_MCP_BACKEND_URL": "'$BACKEND_URL'",
        "ZENTAO_MCP_API_KEY": "'$api_key'"
      }
    }
  }
}'
    
    echo "$config_content" > "$claude_config_dir/claude_desktop_config.json"
    
    log_success "Claude Desktop配置已创建: $claude_config_dir/claude_desktop_config.json"
    
    if [[ "$VERBOSE" == "true" ]]; then
        log_info "配置内容:"
        cat "$claude_config_dir/claude_desktop_config.json"
    fi
}

# 测试配置
test_configuration() {
    log_info "测试MCP客户端配置..."
    
    local api_key=$(get_api_key)
    
    if [[ "$api_key" == "your-api-key-here" ]]; then
        log_warning "使用的是占位符API Key，请手动配置正确的API Key"
        return 1
    fi
    
    # 测试后端连接
    if curl -f -s "$BACKEND_URL/health" &> /dev/null; then
        log_success "后端服务连接正常: $BACKEND_URL"
    else
        log_warning "后端服务连接失败: $BACKEND_URL"
        log_warning "请确保后端服务正在运行"
    fi
    
    # 测试客户端命令
    local client_cmd=$(get_client_command)
    if [[ "$client_cmd" == "uv" ]]; then
        if [[ -d "zentao-mcp-client" ]]; then
            cd zentao-mcp-client
            if uv run python -c "import zentao_mcp_client" 2>/dev/null; then
                log_success "客户端模块导入正常"
            else
                log_warning "客户端模块导入失败，请检查依赖安装"
            fi
            cd ..
        fi
    else
        if zentao-mcp-client --help &> /dev/null; then
            log_success "客户端命令可用"
        else
            log_warning "客户端命令不可用"
        fi
    fi
}

# 显示使用说明
show_usage_instructions() {
    echo ""
    log_success "IDE集成配置完成！"
    echo ""
    echo "📋 下一步操作："
    echo "1. 确保后端服务运行:"
    echo "   ./deploy-backend-universal.sh dev deploy"
    echo ""
    echo "2. 配置客户端连接信息:"
    echo "   zentao-mcp-client configure"
    echo "   # 或者设置环境变量:"
    echo "   export ZENTAO_MCP_BACKEND_URL=\"$BACKEND_URL\""
    echo "   export ZENTAO_MCP_API_KEY=\"your-actual-api-key\""
    echo ""
    echo "3. 重启IDE以加载新配置"
    echo ""
    echo "🔧 测试连接:"
    echo "   zentao-mcp-client start --mode stdio"
    echo ""
    echo "📁 配置文件位置:"
    if [[ "$TARGET_IDE" == "all" || "$TARGET_IDE" == "cursor" ]]; then
        case "$(uname)" in
            Darwin)
                echo "   Cursor: ~/Library/Application Support/Cursor/User/globalStorage/rooveterinaryinc.roo-cline/settings/cline_mcp_settings.json"
                ;;
            Linux)
                echo "   Cursor: ~/.config/Cursor/User/globalStorage/rooveterinaryinc.roo-cline/settings/cline_mcp_settings.json"
                ;;
        esac
    fi
    if [[ "$TARGET_IDE" == "all" || "$TARGET_IDE" == "claude" ]]; then
        case "$(uname)" in
            Darwin)
                echo "   Claude Desktop: ~/Library/Application Support/Claude/claude_desktop_config.json"
                ;;
            Linux)
                echo "   Claude Desktop: ~/.config/Claude/claude_desktop_config.json"
                ;;
        esac
    fi
}

# 主函数
main() {
    echo "🚀 IDE集成配置工具"
    echo "===================="
    
    # 解析命令行参数
    while [[ $# -gt 0 ]]; do
        case $1 in
            --backend-url)
                BACKEND_URL="$2"
                shift 2
                ;;
            --backend-url=*)
                BACKEND_URL="${1#*=}"
                shift
                ;;
            --api-key)
                API_KEY="$2"
                shift 2
                ;;
            --api-key=*)
                API_KEY="${1#*=}"
                shift
                ;;
            --mode)
                CLIENT_MODE="$2"
                shift 2
                ;;
            --mode=*)
                CLIENT_MODE="${1#*=}"
                shift
                ;;
            -v|--verbose)
                VERBOSE=true
                shift
                ;;
            -h|--help)
                show_help
                exit 0
                ;;
            cursor|claude|all)
                TARGET_IDE="$1"
                shift
                ;;
            *)
                log_error "未知参数: $1"
                show_help
                exit 1
                ;;
        esac
    done
    
    # 检查客户端可用性
    check_client_availability
    
    # 配置IDE
    case $TARGET_IDE in
        cursor)
            configure_cursor
            ;;
        claude)
            configure_claude
            ;;
        all)
            configure_cursor
            configure_claude
            ;;
        *)
            log_error "不支持的IDE: $TARGET_IDE"
            exit 1
            ;;
    esac
    
    # 测试配置
    test_configuration
    
    # 显示使用说明
    show_usage_instructions
}

# 执行主函数
main "$@"
